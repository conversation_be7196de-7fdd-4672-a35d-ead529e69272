package com.facishare.paas.appframework.privilege.dto;

import com.google.common.collect.Lists;

import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/1/16 18:03
 */
public interface UpdateFuncCodeRoles {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private String funcCode;
        private List<String> addRoleSet;
        private List<String> delRoleSet;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private boolean success;
    }

}
