package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.UserEntitiesFieldPermissModel;
import com.facishare.paas.appframework.privilege.dto.UserFieldPermissModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Created by luxin on 2018/4/24.
 */
@RestResource(value = "PAAS-PRIVILEGE", desc = "字段权限服务", contentType = "application/json")
@Deprecated
public interface FieldPrivilegeProxy {

    @POST(value = "/userEntitysFieldPermiss", desc = "获取多个对象的字段权限")
    @Deprecated
    UserEntitiesFieldPermissModel.Result listUserEntitiesFiledPrivilege(@Body UserEntitiesFieldPermissModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/userFieldPermiss", desc = "获取单个对象的字段权限")
    @Deprecated
    UserFieldPermissModel.Result getUserFiledPrivilege(@Body UserFieldPermissModel.Arg arg, @HeaderMap Map<String, String> header);

}
