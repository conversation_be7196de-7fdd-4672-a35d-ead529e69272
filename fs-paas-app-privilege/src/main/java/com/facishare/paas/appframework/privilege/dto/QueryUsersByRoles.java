package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/10/23.
 */
public interface QueryUsersByRoles {

    @Data
    class Arg {
        private AuthContext authContext;
        private List<String> roles;

        @Builder
        public Arg(AuthContext context, @Singular List<String> roles) {
            this.authContext = context;
            this.roles = roles;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private Map<String, Object> result;
    }
}
