package com.facishare.paas.appframework.privilege.util;

import com.facishare.crm.openapi.Utils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * Created by zhouwr on 2017/11/1
 */
public interface PrivilegeConstants {

    String APP_SCOPE_ALL = "ALL";
    String APP_ID = "CRM";

    int SYSTEM_DEFINED_FUNCTION_CODE_TYPE = 0;
    int USER_DEFINED_FUNCTION_CODE_TYPE = 1;
    int USER_DEFINED_BUTTON_FUNCTION_CODE_TYPE = 2;


    //销售订单确认发货的funcCode
    String SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE = "6028";
    //发货单新建 funcCode
    String DELIVERY_NOTE_ADD_FUNC_CODE = "DeliveryNoteObj||Add";

    List<String> VISITING_FUNC_CODES = Collections.unmodifiableList(
            Lists.newArrayList("20001", "20002", "20003", "20004", "20005", "20006",
                    "20007", "20008", "20009", "20011", "20012", "20014",
                    "20018", "20019", "20020", "20013", "20015", "20016", "20021", "20022")
    );

    List<String> BI_VISITING_FUNC_CODES = Collections.unmodifiableList(
            Lists.newArrayList("100601", "100602", "100603", "100604", "100605", "100606",
                    "100607", "100608")
    );


    String VISITING_LIST_FUNC_CODE = "20001";

    //销售订单确认收货的funcCode
    String SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE = "6027";
    //发货单确认收货funcCode
    String DELIVERY_NOTE_CONFIRM_RECEIPT_FUNC_CODE = "DeliveryNoteObj||ConfirmReceipt";


    String FUNC_CODE_SPLIT = "||";

    String PAAS_PARENT_CODE = "00000000000000000000000000000000";
    String ADMIN_ROLE_CODE = "00000000000000000000000000000006";
    String SALESMAN_ROLE_CODE = "00000000000000000000000000000015";
    String REPORT_ADMIN_ROLE_CODE = "00000000000000000000000000000017";
    String ORDER_GOODS_ROLE_CODE = "00000000000000000000000000000021";
    String CHECKINS_ROLE_CODE = "00000000000000000000000000000026";
    String SERVICE_PERSON_ROLE_CODE = "00000000000000000000000000000010"; //服务人员
    String CHECKINS_ROLE_DESCRIPTION = "外勤人员预设了使用外勤功能的权限";// ignoreI18n

    String REFUND_FINANCE_CODE = "00000000000000000000000000000004";//退款财务
    String INVOICE_FINANCE_CODE = "00000000000000000000000000000005";//开票财务

    String PERSONNEL_ROLE = "personnelrole";// 所有员工

    Set<String> FUNC_CHECK_WHITE_LIST_API_NAMES_SlaveUseMaster = Sets.newHashSet(
            "LeadsPoolObj",
            "AccountCostObj",
            "ReturnedGoodsInvoiceProductObj", "BpmInstance", "BpmTask", "payment_record", Utils.APPROVAL_INSTANCE_API_NAME,
            Utils.APPROVAL_TASK_API_NAME, "FlowAutoTaskObj"
    );

    Set<String> FUNC_CHECK_WHITE_LIST_API_NAMES = Sets.newHashSet(
            "LeadsPoolObj",
            "AccountCostObj", "SalesOrderProductObj",
            "ReturnedGoodsInvoiceProductObj", "BpmInstance", "BpmTask", "payment_record", Utils.APPROVAL_INSTANCE_API_NAME,
            Utils.APPROVAL_TASK_API_NAME, "FlowAutoTaskObj"
    );

    Set<String> FUNC_CHECK_WHITE_LIST_ACTION_CODES = Sets.newHashSet(
            "SignIn", "SignOut", "Pay", "SaleRecord", "Dial", "SendMail", "Discuss", "Schedule", "Remind",
            "Remove", "Recall", "Confirm", "Reject", "ChangeConfirmor", "AddDeliveryNote", "Audit", "ChangeAuditor", "ProcessLeads",
            "BulkHangTag"
    );

    String SYSTEM_LEVEL_EDIT_ALL_DATA = "edit_all_data";
    String SYSTEM_LEVEL_VIEW_ALL_DATA = "view_all_data";
    String SUFFIX_OBJECT_LEVEL_EDIT_ALL = "EditAll";
    String SUFFIX_OBJECT_LEVEL_VIEW_ALL = "ViewAll";

    /**
     * AI 的个人license，目前是通过权限校验的
     */
    String AI_OBJECT_USER_LICENSE = "AI_Object_User_License";

    interface AccountAttrObj {
        String VIEW_ACTION = "Card_Attach";
        String EDIT_ACTION = "Attach";
        String ADD_BUTTON_LABEL = "上传";// ignoreI18n
    }

    interface ErrorCode {
        int FUNC_CODE_DULPLICATE = *********;
    }
}