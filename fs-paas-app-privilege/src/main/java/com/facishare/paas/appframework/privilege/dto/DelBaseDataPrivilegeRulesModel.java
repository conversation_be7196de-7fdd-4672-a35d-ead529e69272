package com.facishare.paas.appframework.privilege.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by yusb on 2017/4/24.
 */
public class DelBaseDataPrivilegeRulesModel {
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends BasePrivilegeArg {
        private List<String> entitys;

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BasePrivilegeResult {
    }
}
