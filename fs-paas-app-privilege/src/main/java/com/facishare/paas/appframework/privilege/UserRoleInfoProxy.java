package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.rest.core.exception.RestProxyRuntimeException;

import java.util.Map;

/**
 * Created by zhouwr on 2017/10/16
 */
@RestResource(value = "PAAS-PRIVILEGE", desc = "用户角色服务", contentType = "application/json")
public interface UserRoleInfoProxy {

    @POST(value = "/roleUser", desc = "查询拥有指定角色的用户")
    GetUsersByRole.Result getUsersByRole(@Body GetUsersByRole.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/userRoleInfo", desc = "查询用户角色信息")
    GetUserRoleInfo.Result getUserRoleInfo(@Body GetUserRoleInfo.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/getMultiEmployeeRoleRelationEntitiesByEmployeeIDs", desc = "根据用户列表获取所有用户角色关系")
    GetRolesByUserId.Result GetRolesByUserId(@Body GetRolesByUserId.Arg arg, @HeaderMap Map<String, String> header) throws RestProxyRuntimeException;

    @POST(value = "/queryRoleUsersByRoles", desc = "批量查询角色下用户")
    QueryUsersByRoles.Result queryRoleUsersByRoles(@Body QueryUsersByRoles.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/queryUsers", desc = "查询用户")
    QueryUsers.Result queryUsers(@Body QueryUsers.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/defaultRole/queryRoleInfoListByUsers", desc = "查询指定条件的用户角色关联")
    QueryRoleInfoListByUsersModel.Result queryRoleInfoListByUsers(@Body QueryRoleInfoListByUsersModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/roleInfo", desc = "角色查询")
    RoleInfoModel.Result roleInfo(@Body RoleInfoModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/queryRoleInfoWithCodes", desc = "多roleCode查询角色信息")
    QueryRoleInfoWithCodes.Result queryRoleInfoWithCodes(@Body QueryRoleInfoWithCodes.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/forbidden/users", desc = "批量添加/删除禁用员工接口")
    ForbiddenUsers.Result forbiddenUsers(@Body ForbiddenUsers.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/defaultRole/queryRoleInfoListByForbiddenUsers", desc = "查询禁用员工信息")
    QueryRoleInfoListByForbiddenUsers.Result queryRoleInfoListByForbiddenUsers(@Body QueryRoleInfoListByForbiddenUsers.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/queryRoleCodeListByUserId", desc = "查询用户分配的角色Code")
    GetRoleCodesByUserId.Result getRoleCodesByUserId(@Body GetRoleCodesByUserId.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/query/role/byName", desc = "根据角色名称查询codes")
    QueryRoleCodeByNames.Result queryRoleCodeByNames(@Body QueryRoleCodeByNames.Arg arg, @HeaderMap Map<String, String> header);


    @POST(value = "/user/crm/check", desc = "校验是否是CRM管理员")
    CrmAdminCheck.Result crmAdminCheck(@Body CrmAdminCheck.Arg arg, @HeaderMap Map<String, String> header);
}
