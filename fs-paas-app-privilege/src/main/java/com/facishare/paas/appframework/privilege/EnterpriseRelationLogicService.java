package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.SupportEnterpriseRelationResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * create by zhaoju on 2021/08/25
 */
public interface EnterpriseRelationLogicService {

    void fillOutOwner(User user, IObjectDescribe describe, Collection<IObjectData> dataList);

    /**
     * 补充相关团队
     *
     * @param user
     * @param describe
     * @param dataList
     */
    void fillOutTeamMember(User user, IObjectDescribe describe, Collection<IObjectData> dataList);

    /**
     * 根据数据中的外部负责人回填外部企业字段
     *
     * @param user     用户信息
     * @param describe 对象描述
     * @param dataList 数据
     */
    void syncOutTenantIdFromOutUser(User user, IObjectDescribe describe, Collection<IObjectData> dataList);

    /**
     * 根据『客户』、『联系人』查询对应的下游主负责人
     *
     * @param user                   用户信息
     * @param objectApiName          对象 apiName
     * @param referenceObjectDataIds 『客户』、『联系人』 id
     * @return 数据 id 和下游人员关系
     */
    Map<String, User> getOwnerOutUserByDataIds(User user, String objectApiName, Set<String> referenceObjectDataIds);

    boolean supportInterconnectBaseAppLicense(String tenantId);

    SupportEnterpriseRelationResult isSupportEnterpriseRelation(String tenantId);

    List<UserInfo> listOuterInfo(User user, Collection<String> names);

    List<UserInfo> getOutUserInfoByEnterpriseAndNames(User user, Collection<String> names);
}
