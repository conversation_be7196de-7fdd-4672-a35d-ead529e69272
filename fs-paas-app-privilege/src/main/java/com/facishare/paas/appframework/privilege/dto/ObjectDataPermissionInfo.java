package com.facishare.paas.appframework.privilege.dto;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * Created by zhouwr on 2017/10/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ObjectDataPermissionInfo {

    //对象ID
    @JsonProperty("ObjectDescribeApiName")
    @SerializedName("ObjectDescribeApiName")
    private String objectDescribeApiName;
    //对象名称
    @JsonProperty("ObjectDescribeDisplayName")
    @SerializedName("ObjectDescribeDisplayName")
    private String objectDescribeDisplayName;
    //权限类型
    @JsonProperty("PermissionType")
    @SerializedName("PermissionType")
    //DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION  .getValue
    private String permissionType;


    public static ObjectDataPermissionInfo create(String describeApiName, String displayName, String permissionType) {
        return new ObjectDataPermissionInfo(describeApiName, displayName, permissionType);
    }

    public LogInfo.ObjectSnapshot toSnapshot(EntityOpennessPojo old) {
        String textMessage = I18N.text(I18NKey.DATA_PERMISSION, objectDescribeDisplayName,
                old.getPermissionFromEntityOpennessPojo().getLabel(),
                DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.getByValue(permissionType).getLabel());
        return LogInfo.ObjectSnapshot.builder()
                .textMsg(Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, objectDescribeApiName)))
                .snapshot(JSON.parseObject(JSON.toJSONString(this)))
                .build();
    }

    public boolean isChange(EntityOpennessPojo entityOpennessPojo) {
        if (entityOpennessPojo == null) return false;
        return Objects.equals(getObjectDescribeApiName(), entityOpennessPojo.getEntityId())
                && !Objects.equals(entityOpennessPojo.getPermissionFromEntityOpennessPojo().getValue(), getPermissionType());
    }
}
