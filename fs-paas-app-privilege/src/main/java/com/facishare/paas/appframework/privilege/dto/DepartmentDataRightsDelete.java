package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface DepartmentDataRightsDelete {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        DepartmentDataRightsContext context;
        int scene;
        Map<String,List<String>> datas;
    }

    class Result {

    }
}
