package com.facishare.paas.appframework.privilege;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.organization.adapter.api.permission.model.CheckFunctionCodeAndGetManageDepartments;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdsByDepartmentId;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeeIdsArg;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.privilege.dto.JudgeManageRangeInfo;
import com.facishare.paas.appframework.privilege.dto.JudgeMangePrivilegeResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum.PERMISSION_BUSINESS_ROLE_QUERY;

/**
 * 判断用户的管理权限范围；
 */

@Slf4j
@Service
public class JudgeMangePrivilegeServiceImpl implements JudgeMangePrivilegeService {

    private static final String APP_ID = "facishare-system";
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private PermissionService permissionService;

    /**
     * 判断用户是否有管理权限
     *
     * @param sessionContext
     * @return 是否有管理权限、管理部门ids、管理员工
     * <p>
     * NPE 注意：在无管理员权限时，result中的部门链表为空，所管理员工为空。
     * 管理权限问题：若管理员为全公司管理员，部门链表返回99999(Integer)
     */
    @Override
    public JudgeMangePrivilegeResult judgeMangePrivilege(SessionContext sessionContext) {
        JudgeMangePrivilegeResult result = new JudgeMangePrivilegeResult();
        CheckFunctionCodeAndGetManageDepartments.Result privilege = checkTargetFunction(sessionContext, PERMISSION_BUSINESS_ROLE_QUERY.getFunctionCode());
        if (privilege.getHasAbility()) {
            List<String> employees = getEmployeeByDepartment(privilege.getDepartmentIds(), sessionContext);
            result.setManageWhole(BooleanUtils.isTrue(privilege.getIsManageWholeCompany()));
            result.setEmployees(employees);
            result.setHavePrivilege(privilege.getHasAbility());
            result.setDeptIds(privilege.getDepartmentIds());
        } else {
            result.setHavePrivilege(privilege.getHasAbility());
        }
        return result;
    }

    @Override
    public JudgeMangePrivilegeResult judgeMangePrivilegeGray(SessionContext sessionContext) {
        JudgeMangePrivilegeResult result = new JudgeMangePrivilegeResult();
        CheckFunctionCodeAndGetManageDepartments.Result privilege = checkTargetFunction(sessionContext, PERMISSION_BUSINESS_ROLE_QUERY.getFunctionCode());
        if (privilege.getHasAbility()) {
            result.setManageWhole(BooleanUtils.isTrue(privilege.getIsManageWholeCompany()));
            if (!BooleanUtils.isTrue(privilege.getIsManageWholeCompany())) {
                List<String> employees = getEmployeeByDepartment(privilege.getDepartmentIds(), sessionContext);
                result.setEmployees(employees);
            }
            result.setHavePrivilege(privilege.getHasAbility());
            result.setDeptIds(privilege.getDepartmentIds());
        } else {
            result.setHavePrivilege(privilege.getHasAbility());
        }
        return result;
    }

    /**
     * 查询该部门下的所有员工
     *
     * @param departmentIds
     * @param sessionContext
     * @return 员工列表
     */
    @Override
    public List<String> getEmployeeByDepartment(List<Integer> departmentIds, SessionContext sessionContext) {
        if (CollectionUtils.empty(departmentIds)) {
            return Lists.newArrayListWithCapacity(0);
        }

        BatchGetEmployeeIdsByDepartmentId.Arg argument = new BatchGetEmployeeIdsByDepartmentId.Arg();
        argument.setEnterpriseId(sessionContext.getEId().intValue());
        argument.setDepartmentIds(departmentIds);
        argument.setRunStatus(RunStatus.ACTIVE);
        argument.setIncludeLowDepartment(true);

        BatchGetEmployeeIdsByDepartmentId.Result employees = employeeProviderService.batchGetEmployeeIdsByDepartmentId(argument);

        return employees.getEmployeeIds().stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public CheckFunctionCodeAndGetManageDepartments.Result checkTargetFunction(SessionContext sessionContext, String targetFunction) {
        CheckFunctionCodeAndGetManageDepartments.Argument argument = new CheckFunctionCodeAndGetManageDepartments.Argument();
        argument.setFunctionCode(targetFunction);
        argument.setAppId(APP_ID);
        argument.setEnterpriseId(sessionContext.getEId().intValue());
        argument.setCurrentEmployeeId(sessionContext.getUserId());
        argument.setEmployeeId(sessionContext.getUserId());
        log.info("call permissionService.checkFunctionCodeAndAllGetManageDepartments with argument:{}", argument);
        return permissionService.checkFunctionCodeAndGetManageDepartments(argument);
    }

    @Override
    public JudgeManageRangeInfo.Result getUserMangeRangeInfo(SessionContext sessionContext, String targetFunction) {
        CheckFunctionCodeAndGetManageDepartments.Result checkTargetFunction = checkTargetFunction(sessionContext, targetFunction);

        JudgeManageRangeInfo.Result result = new JudgeManageRangeInfo.Result();

        result.setHasAbility(checkTargetFunction.getHasAbility());
        if (Objects.equals(checkTargetFunction.getIsManageWholeCompany(), Boolean.TRUE)) {
            result.setManageWholeCompany(true);
            result.setManageRangeUserIds(getEmployeeIdsByEnterpriseId(sessionContext));
        } else {
            result.setManageRangeUserIds(getEmployeeByDepartment(checkTargetFunction.getDepartmentIds(), sessionContext));
        }
        return result;
    }

    @NotNull
    private List<String> getEmployeeIdsByEnterpriseId(SessionContext sessionContext) {
        final GetAllEmployeeIdsArg arg = new GetAllEmployeeIdsArg();
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setEnterpriseId(sessionContext.getEId().intValue());
        return employeeProviderService.getAllEmployeeIds(arg).getEmployeeIds().stream().map(String::valueOf).collect(Collectors.toList());
    }

}
