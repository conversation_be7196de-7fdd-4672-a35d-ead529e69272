package com.facishare.paas.appframework.privilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.paas.appframework.privilege.util.AppIdUtil;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.facishare.organization.adapter.api.permission.enums.functioncode.SystemFunctionCodeEnum.PERMISSION_BUSINESS_ROLE_EXPORT;
import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;


/**
 * <AUTHOR>
 * @date 2018/2/26 10:56
 */
@Service
@Slf4j
public class ExportUserServiceImpl implements ExportUserService {
    @Autowired
    private GDSHandler gdsService;

    @Autowired
    private UserRoleInfoProxy userRoleInfoProxy;

    @Autowired
    private OrgService orgService;

    @Autowired
    private JudgeMangePrivilegeService judgeMangePrivilegeService;

    @Resource
    private NFileStorageService nFileStorageService;


    @Override
    public Result exportUserByRole(User user, String roleCode) {
        String tenantId = user.getTenantId();
        SessionContext sessionContext = new SessionContext();
        sessionContext.setEId(Long.valueOf(tenantId));
        sessionContext.setUserId(Integer.valueOf(user.getUserId()));

        if (Strings.isNullOrEmpty(roleCode)) {
            roleCode = "";
        }
        JudgeManageRangeInfo.Result userMangeRangeInfo = judgeMangePrivilegeService.getUserMangeRangeInfo(sessionContext, PERMISSION_BUSINESS_ROLE_EXPORT.getFunctionCode());
        if (userMangeRangeInfo.isHasAbility()) {
            try {
                Workbook wb = new SXSSFWorkbook(500);
                // 2018/2/26 获取roleName
                String title;
                List<String> labels = Arrays.asList(I18N.text(I18NKey.NAME), I18N.text(I18NKey.MAIN_DEPARTMENT),
                        I18N.text(I18NKey.POSITION), I18N.text(I18NKey.MASTER_ROLE), I18N.text(I18NKey.ROLE));
                Sheet sheet = createSheetWithTitle(wb, roleCode, labels);
                AuthContext context = buildAuthContext(user);

                List<List<String>> data = Lists.newArrayList();
                if (!Strings.isNullOrEmpty(roleCode)) {
                    //2018/2/26 获取某角色用户

                    RoleInfoModel.Arg roleInfoArg = new RoleInfoModel.Arg(context, roleCode);
                    RoleInfoModel.Result roleInfoModelResult = userRoleInfoProxy.roleInfo(roleInfoArg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
                    title = roleInfoModelResult.getResult().get("roles").get(0).getRoleName();
                    GetUsersByRole.Arg arg = new GetUsersByRole.Arg();
                    arg.setAuthContext(context);
                    arg.setRoleCode(roleCode);
                    GetUsersByRole.Result result = userRoleInfoProxy.getUsersByRole(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
                    if (!result.isSuccess()) {
                        throw new PermissionError(result.getErrMessage());
                    }
                    List<String> userIds = getUserIds(sessionContext, result.getResult().getUsers());
                    List<UserInfo> userInfos = orgService.getUserNameByIds(tenantId, user.getUserId(), userIds);
                    Map<String, UserInfo> userInfoMap = Maps.newHashMap();
                    for (UserInfo userInfo : userInfos) {
                        userInfoMap.put(userInfo.getId(), userInfo);
                    }

                    Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfos = orgService.getMainDeptInfo(tenantId, user.getUserId(), userIds);

                    for (String userId : userIds) {
                        if (userInfoMap.containsKey(userId)) {
                            UserInfo userInfo = userInfoMap.get(userId);
                            List<String> userData = Lists.newArrayList();
                            userData.add(userInfo.getName());
                            QueryDeptInfoByUserIds.MainDeptInfo mainDeptInfo = mainDeptInfos.get(userInfo.getId());
                            if (mainDeptInfo == null) {
                                userData.add("-");
                            } else {
                                userData.add(mainDeptInfo.getDeptName());
                            }
                            userData.add(userInfo.getPost());
                            data.add(userData);
                        }
                    }
                } else {
                    //  2018/2/26 获取全部用户
                    title = I18N.text(I18NKey.STAFF_ROLE_DISTRIBUTION);
                    QueryUsers.Arg queryUsersArg = new QueryUsers.Arg(context,null, null);
                    QueryUsers.Result queryUsersResult = userRoleInfoProxy.queryUsers(queryUsersArg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
                    if (!queryUsersResult.isSuccess()) {
                        throw new PermissionError(queryUsersResult.getErrMessage());
                    }

                    List<String> users = getUserIds(sessionContext, queryUsersResult.getResult().get("users"));

                    QueryRoleInfoListByUsersModel.Arg queryRoleInfoListByUsersModelArg =
                            new QueryRoleInfoListByUsersModel.Arg(context, users, Lists.newArrayList("00000000000000000000000000000021"));
                    QueryRoleInfoListByUsersModel.Result queryRoleInfoListByUsersModelResult =
                            userRoleInfoProxy.queryRoleInfoListByUsers(queryRoleInfoListByUsersModelArg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
                    Map<String, List<QueryRoleInfoListByUsersModel.UserRolePojo>> userRolePojos = queryRoleInfoListByUsersModelResult.getResult().get("userRoles");

                    Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfos = orgService.getMainDeptInfo(tenantId, user.getUserId(), users);

                    List<UserInfo> userInfos = orgService.getUserNameByIds(tenantId, user.getUserId(), users);
                    Map<String, UserInfo> userInfoMap = Maps.newHashMap();
                    for (UserInfo userInfo : userInfos) {
                        userInfoMap.put(userInfo.getId(), userInfo);
                    }

                    for (String userId : users) {
                        if (userInfoMap.containsKey(userId)) {
                            UserInfo userInfo = userInfoMap.get(userId);
                            List<QueryRoleInfoListByUsersModel.UserRolePojo> userInfo1 = userRolePojos.get(userInfo.getId());
                            if (userInfo1 == null) continue;
                            List<String> userData = Lists.newArrayList();
                            userData.add(userInfo.getName());
                            QueryDeptInfoByUserIds.MainDeptInfo mainDeptInfo = mainDeptInfos.get(userInfo.getId());
                            if (mainDeptInfo == null) {
                                userData.add("");
                            } else {
                                userData.add(mainDeptInfo.getDeptName());
                            }
                            userData.add(userInfo.getPost());

                            StringBuilder allRoles = new StringBuilder("");
                            for (QueryRoleInfoListByUsersModel.UserRolePojo queryRoleInfoListByUsersModelUserInfo : userInfo1) {
                                allRoles.append(queryRoleInfoListByUsersModelUserInfo.getRoleName() + " ");
                                if (queryRoleInfoListByUsersModelUserInfo.getDefaultRole()) {
                                    userData.add(queryRoleInfoListByUsersModelUserInfo.getRoleName());
                                }
                            }
                            userData.add(allRoles.toString());
                            data.add(userData);
                        }
                    }
                }
                generateSheet(sheet, 0, data);
                String path = exportData(wb, user.getTenantId(), user.getUserId());
                return Result.builder().ext("xlsx").file_name(title).path(path).build();
            } catch (Exception e) {
                log.error("Failed in generating excel, user:{}", user, e);
            }
        } else {
            throw new PermissionError(I18N.text(I18NKey.FUNCTION_PRIVILEGE_NOT_HAVE_EXPORT_PRIVILEGE));
        }
        return null;
    }

    /**
     * 获取过滤了分管权限的员工列表
     *
     * @param sessionContext
     * @param allUserIds
     * @return
     */
    private List<String> getUserIds(SessionContext sessionContext, List<String> allUserIds) {
        JudgeMangePrivilegeResult judgeMangePrivilegeResult = judgeMangePrivilegeService.judgeMangePrivilege(sessionContext);

        List<String> userIds = ListUtils.intersection(allUserIds, judgeMangePrivilegeResult.getEmployees());
        return userIds;
    }

    /**
     * 将文件上传至服务器兵返回地址
     */
    String exportData(Workbook exportDataWorkbookResult, String tenantId, String userId) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            exportDataWorkbookResult.write(outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("Error in export data excel to inputStream,", e);
            throw e;
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("Error in closing stream when uploading excel", e);
            }
        }

        //上传文件到服务器并返回path
        String ea = gdsService.getEAByEI(tenantId);
        return uploadFile(ea, userId, outputStream);
    }

    String uploadFile(String ea, String user, ByteArrayOutputStream outputStream) {
        byte[] data = outputStream.toByteArray();
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setEa(ea);
        arg.setSourceUser(user);
        arg.setData(data);

        NTempFileUpload.Result result = nFileStorageService.nTempFileUpload(arg, ea);
        if (null == result || Strings.isNullOrEmpty(result.getTempFileName())) {
            log.error("Can not upload excel file by calling nTempUploadByChunkStart");
            throw new MetaDataException("Can not upload excel file by calling nTempUploadByChunkStart");
        }

        return result.getTempFileName();
    }

    /**
     * 创建列名
     */
    Sheet createSheetWithTitle(Workbook workbook, String roleCode, List<String> labels) {
        Sheet exportDataNewSheet = workbook.createSheet(I18N.text(I18NKey.STAFF_ROLE));
        Row newTitleRow = exportDataNewSheet.createRow(0);
        int headerIndex = 0;
        if (Strings.isNullOrEmpty(roleCode)) {
            for (String label : labels) {
                newTitleRow.createCell(headerIndex++).setCellValue(label);
            }
        } else {
            for (int i = 0; i < labels.size() - 2; i++) {
                String label = labels.get(i);
                newTitleRow.createCell(headerIndex++).setCellValue(label);
            }
        }

        return exportDataNewSheet;
    }

    /**
     * 将数据写入表格
     */
    void generateSheet(Sheet exportDataNewSheet, int index, List<List<String>> data) {
        for (int i = 1; i <= data.size(); i++) {
            Row row = exportDataNewSheet.createRow(index + i);
            int columnIndex = 0;
            //IObjectData objectData = objectDataList.get(i - 1);
            for (String cellData : data.get(i - 1)) {
                // String value = objectData.get(fieldDescribe.getApiName(), String.class);
                if (com.google.common.base.Strings.isNullOrEmpty(cellData)) {
                    row.createCell(columnIndex++).setCellValue("");
                } else {
                    row.createCell(columnIndex++).setCellValue(cellData);
                }
            }
        }
    }

    private AuthContext buildAuthContext(User user) {
        return AuthContext.buildByUser(user);
    }

    @Data
    @Builder
    public static class Result {

        @JSONField(name = "M1")
        String ext;

        @JSONField(name = "M2")
        String file_name;

        @JSONField(name = "M3")
        String path;

        @JSONField(name = "M4")
        String token;

        @JSONField(name = "M5")
        String message;


    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        private String roleCode;
    }

}
