package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/12/13
 */
@Component
public class AccountAttFunctionPrivilegeProvider implements FunctionPrivilegeProvider {

    @Override
    public String getApiName() {
        return ObjectAPINameMapping.AccountAtt.getApiName();
    }

    @Override
    public List<String> getSupportedActionCodes() {
        return Collections.emptyList();
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        return Collections.emptyMap();
    }

}
