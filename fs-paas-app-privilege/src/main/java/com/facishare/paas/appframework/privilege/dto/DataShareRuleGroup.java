package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Set;

@Data
@Builder
public class DataShareRuleGroup {
  String groupId;

  Set<String> describeApiNames;

  Integer permissionType;

  Set<String> sourceDeptIds;

  Set<String> sourceEmployeeIds;

  Set<String> sourceUserGroupIds;

  Set<String> sourceRoleIds;

  Set<String> targetDeptIds;

  Set<String> targetEmployeeIds;

  Set<String> targetUserGroupIds;

  Set<String> targetRoleIds;

  Integer basedType;

  //是否包含子部门 1 包含 0/null  不包含
  Integer receiveDeptCascade;
}
