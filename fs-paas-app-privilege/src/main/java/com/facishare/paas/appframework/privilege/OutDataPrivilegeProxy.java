package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.GetObjectOutDataPrivilege;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 外部联系人数据权限
 * <p>
 * Created by liyi<PERSON><PERSON> on 2018/7/18.
 */
@RestResource(value = "OUTUSER-DATA-PRIVILEGE", desc = "外部联系人数据权限", contentType = "application/json"
        , codec = "com.facishare.paas.appframework.privilege.OutDataPrivilegeProxy$Codec")
public interface OutDataPrivilegeProxy {

    /**
     * 外部数据权限
     *
     * @param arg
     * @return
     */
    @POST(value = "/getByUserAndObjectApiName", desc = "获取外部数据权限")
    Integer getObjectOutDataPrivilege(@Body GetObjectOutDataPrivilege.Arg arg);

    class Codec implements IRestCodeC {

        private static final ObjectMapper objectMapper = new ObjectMapper();

        static {
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
            objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        }

        @Override
        public <T> byte[] encodeArg(T obj) {
            try {
                return objectMapper.writeValueAsBytes(obj);
            } catch (IOException e) {
                throw new RuntimeException("encode error", e);
            }
        }

        @Override
        public <T> T decodeResult(int statusCode, Map<String, List<String>> map, byte[] bytes, Class<T> aClass) {

            if (statusCode != 200) {
                throw new RuntimeException("decode error,body:" + (bytes != null ? new String(bytes) : ""));
            }
            try {
                JsonNode root = objectMapper.readTree(bytes);
                int errCode = root.get("errCode").asInt();
                if (errCode != 0) {
                    JsonNode errMsgNode = root.get("errMsg");
                    String errorMsg = Objects.nonNull(errMsgNode) ? errMsgNode.asText() : "Empty Error Message";
                    throw new RestProxyBusinessException(errCode, errorMsg);
                }
                return objectMapper.readValue(root.get("data").asText(), aClass);
            } catch (IOException e) {
                throw new RuntimeException("decode error", e);
            }
        }
    }
}
