package com.facishare.paas.appframework.privilege.util;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/03/17
 */
public enum Headers {
    PAAS_PRIVILEGE_HEADDER,
    ;

    public Map<String, String> buildHeader(String ei) {
        Map<String, String> ret = Maps.newHashMap();
        ret.put("x-fs-ei", ei);
        return ret;
    }

    public Map<String, String> defaultHeader() {
        String tenantId = RequestContextManager.getContext().getTenantId();
        return buildHeader(tenantId);
    }
}
