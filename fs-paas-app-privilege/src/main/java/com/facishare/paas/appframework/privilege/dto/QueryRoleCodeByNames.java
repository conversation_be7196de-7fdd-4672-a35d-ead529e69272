package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QueryRoleCodeByNames {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private List<String> roleNames;
        private Integer roleType;
    }


    @Data
    class Result extends BasePrivilegeResult {
        private RoleResult result;
    }

    @Data
    class RoleResult {
        private List<RoleInfo> roles;
    }

    @Data
    class RoleInfo {
        private String roleCode;
        private String roleName;
        private Integer roleType;
    }
}
