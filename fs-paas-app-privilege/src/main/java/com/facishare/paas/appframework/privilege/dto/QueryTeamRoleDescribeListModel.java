package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
import java.util.Set;

public interface QueryTeamRoleDescribeListModel {

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {

    }


    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private Map<String, Set<String>> result;
    }
}
