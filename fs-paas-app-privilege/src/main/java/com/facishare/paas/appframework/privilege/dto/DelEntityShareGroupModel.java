package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class DelEntityShareGroupModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Arg extends BasePrivilegeArg {
    private Set<String> groupIds;
    private Boolean returnList;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BasePrivilegeResult {
    private Map<Integer, List<EntitySharePojo>> result;
  }
}
