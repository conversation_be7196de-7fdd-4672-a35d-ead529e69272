package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface BatchDeleteTemporaryPrivilege {

  @Data
  class Arg {
    private List<String> userIds;
    private List<String> outUserIds;
    private List<String> describeApiNames;
    private List<String> scenes;
    private List<String> dataIds;
    private Map<String, Map<String, Long>> createTimeRange;
  }


  @Data
  @Builder
  class Result {
    private boolean success;
    private String message;
  }

}
