package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2018/1/16.
 */
public interface FuncCodePrivilege {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private AuthContext authContext;
        private List<String> funcCodes;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private Map<String, List<String>> result;
        private boolean success;
    }

}
