package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class QueryDimensionRuleGroupModel {

  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Arg extends BasePrivilegeArg {

    private Set<String> receiveIds;

    private Integer receiveType;

    private String receiveTenantId;

    private int permissionType;

    private Map<String, Long> createTimeRange;

    private Map<String, Long> modifyTimeRange;

    private int sortType;

    private int sortOrder;

    private BasePageInfoDataPrivilege pageInfo;

  }


  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Result extends BasePrivilegeResult {
    private QueryDimensionRuleGroupPojoResult result;


    @Data
    public static class QueryDimensionRuleGroupPojoResult {

      private PageInfo page;

      private List<DimensionRuleGroupPojo> content;

    }
  }

}
