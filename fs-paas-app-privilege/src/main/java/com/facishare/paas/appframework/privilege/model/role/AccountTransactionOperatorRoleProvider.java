package com.facishare.paas.appframework.privilege.model.role;

import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * Created by luxin on 2018/7/24.
 */
@Component
public class AccountTransactionOperatorRoleProvider extends RoleProvider {
    private static Set<String> havePermissFuncCodes;

    static {
        ConfigFactory.getConfig("fs-crm-privilege-role-func", config -> {
            havePermissFuncCodes = Collections.unmodifiableSet(Sets.newHashSet(config.get("accountTransactionOperatorHavePermissFuncCodes").split(",")));
        });
    }

    @Override
    public Role getRole() {
        return null;
    }

    @Override
    public Map<String, Boolean> getFuncCodeAndEditableMapping() {
        return null;
    }

    @Override
    public Set<String> getHavePermissFuncCodes() {
        return havePermissFuncCodes;
    }
}
