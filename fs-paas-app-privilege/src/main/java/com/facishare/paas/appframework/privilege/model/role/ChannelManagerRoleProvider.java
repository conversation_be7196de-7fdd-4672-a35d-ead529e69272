package com.facishare.paas.appframework.privilege.model.role;

import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * Created by luxin on 2018/5/28.
 */
@Component
public class ChannelManagerRoleProvider extends RoleProvider {
    private static Set<String> havePermissFuncCodes;

    static {
        ConfigFactory.getConfig("fs-crm-privilege-role-func", config -> {
            havePermissFuncCodes = Collections.unmodifiableSet(Sets.newHashSet(config.get("channelMangerHavePermissFuncCodes").split(",")));
        });
    }

    @Override
    public Role getRole() {
        return Role.CHANNEL_MANAGER;
    }

    @Override
    public Map<String, Boolean> getFuncCodeAndEditableMapping() {
        // TODO: 2018/5/28 待考虑后实现
        return null;
    }

    @Override
    public Set<String> getHavePermissFuncCodes() {
        return havePermissFuncCodes;
    }
}
