package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2020/5/15
 */
public interface CrmAdminCheck {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private List<String> users;
    }

    @Data
    class Result {
        private int errCode;
        private String errMessage;
        private Map<String, Boolean> result;

        public boolean isSuccess() {
            return this.errCode == 0;
        }
    }

}
