package com.facishare.paas.appframework.privilege;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege;
import com.facishare.paas.appframework.privilege.dto.UpdateFunc;
import com.facishare.paas.appframework.privilege.dto.UpdateFuncCodeRoles;
import com.facishare.paas.appframework.privilege.util.ActionCodeConvertUtil;
import com.facishare.paas.appframework.privilege.util.AuthContextExt;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by luxin on 2018/1/15.
 */
@Service
@Slf4j
public class UserDefinedButtonServiceImpl implements UserDefinedButtonService {

    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;


    @Override
    public Boolean createUserDefinedButton(User user, String describeApiName, String buttonApiName, String buttonName, List<String> addRoles) {
        ObjectAction objectAction = ObjectAction.getByButtonApiName(buttonApiName);
        if (ObjectAction.UNKNOWN_ACTION != objectAction) {
            buttonApiName = objectAction.getActionCode();
            buttonName = objectAction.getActionLabel();
        }
        String actionCode = buttonApiName;
        functionPrivilegeService.createFuncCode(user, describeApiName, actionCode, buttonName);
        ParallelUtils.createBackgroundTask().submit(() -> {
            try {
                functionPrivilegeService.rolesAddFuncAccess(user, describeApiName, actionCode, addRoles);
            } catch (Exception e) {
                log.warn("rolesAddFuncAccess fail, ei:{}, describeApiName:{}, actionCode:{}, addRoles:{}",
                        user.getTenantId(), describeApiName, actionCode, addRoles);
            }
        }).run();
        return Boolean.TRUE;
    }


    @Override
    public Boolean updateUserDefinedButtonPrivilegeRoles(User user, String describeApiName, String buttonApiName, List<String> addRoles, List<String> delRoles) {
        UpdateFuncCodeRoles.Arg arg = UpdateFuncCodeRoles.Arg.builder()
                .authContext(buildAuthContext(user))
                .funcCode(ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName))
                .addRoleSet(addRoles)
                .delRoleSet(delRoles)
                .build();
        UpdateFuncCodeRoles.Result result = functionPrivilegeProxy.updateFuncCodeRoles(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));

        if (!result.isSuccess()) {
            log.warn("updateFuncCodeRoles error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.UPDATE_ROLE_LIST_PERMISSION_FAIL_REASON));
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateFuncButtonName(User user, String describeApiName, String buttonApiName, String buttonName) {
        UpdateFunc.Arg arg = UpdateFunc.Arg.builder()
                .authContext(buildAuthContext(user))
                .functionPojo(getFunctionPojo(user.getTenantId(), describeApiName, buttonApiName, buttonName))
                .build();
        UpdateFunc.Result result = functionPrivilegeProxy.updateFunc(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
        if (!result.isSuccess()) {
            if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(describeApiName)) {
                return Boolean.TRUE;
            }
            log.warn("updateFuncCodeRoles error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.UPDATE_CUSTOM_BUTTON_FAIL));
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteUserDefinedButton(User user, String describeApiName, String buttonApiName) {
        functionPrivilegeService.deleteUserDefinedActionCode(user, describeApiName, buttonApiName);
        return Boolean.TRUE;
    }

    @Override
    public List<String> getHavePrivilegeRolesByUserDefinedButton(User user, String describeApiName, String buttonApiName) {
        return functionPrivilegeService.getHavePrivilegeRolesByActionCode(user, describeApiName, buttonApiName);
    }


    private AuthContext buildAuthContext(User user) {
        return AuthContext.builder()
                .appId(AuthContextExt.getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .properties(AuthContextExt.buildProperties(user))
                .identityType(RequestUtil.getOutIdentityType())
                .build();
    }

    @NotNull
    private CreateFunctionPrivilege.FunctionPojo getFunctionPojo(String tenantId, String describeApiName, String buttonApiName, String buttonName) {
        CreateFunctionPrivilege.FunctionPojo functionPojo = new CreateFunctionPrivilege.FunctionPojo();
        functionPojo.setAppId(PrivilegeConstants.APP_ID);
        functionPojo.setTenantId(tenantId);
        functionPojo.setFuncName(buttonName);
        functionPojo.setFuncCode(ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName));
        functionPojo.setParentCode(PrivilegeConstants.PAAS_PARENT_CODE);
        return functionPojo;
    }


}
