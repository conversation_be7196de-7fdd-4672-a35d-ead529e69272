package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by zhouwr on 2017/10/16
 */
public interface UserResponsibleDeptUsers {
    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private Boolean deptCascade;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private List<String> result;
    }
}
