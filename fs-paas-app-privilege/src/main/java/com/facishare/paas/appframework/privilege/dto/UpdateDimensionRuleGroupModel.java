package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

public class UpdateDimensionRuleGroupModel {

  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Arg extends BasePrivilegeArg {
    private DimensionRuleGroupPojo dimensionRuleGroup;
  }


  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Result extends BasePrivilegeResult {
    private String result;
  }

}
