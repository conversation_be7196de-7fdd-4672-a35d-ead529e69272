package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by zhouwr on 2017/10/16
 */
public interface GetDataPrivilegeRules {
    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private List<String> entitys;
        private Integer permission;
        private Integer scope;
        private PageInfo page;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private ResultData result;
    }

    @Data
    class ResultData {
        private List<Openness> content;
        private PageInfo page;
    }

    @Data
    class Openness {
        private String id;
        private String entityId;
        private Integer permission;
        private Integer scope;
        private String appId;
        private String tenantId;
        private Boolean delFlag;
        private String creator;
        private Long createTime;
        private String modifier;
        private Long modifyTime;
    }

    int SCOPE_ALL = 0; //全公司
    int SCOPE_DEPT = 1; //本部门
    int SCOPE_PRIVATE = 2; //私有
}
