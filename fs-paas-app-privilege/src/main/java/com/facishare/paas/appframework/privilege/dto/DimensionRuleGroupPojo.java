package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;

import java.util.List;

@Data
public class DimensionRuleGroupPojo {

  private String id;
  private String tenantId;
  private String entityId;
  private String ruleCode;
  private String ruleParse;
  private Integer ruleType;
  private Integer permission;
  private String remark;
  private Integer status;
  private String creator;
  private Long createTime;
  private String modifier;
  private Long modifyTime;
  private List<DimensionRulePojo> rules;
  private List<DimensionRuleGroupReceivePojo> receives;

}
