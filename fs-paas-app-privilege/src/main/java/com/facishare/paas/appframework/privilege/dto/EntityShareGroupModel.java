package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface EntityShareGroupModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  @Builder
  class Arg extends BasePrivilegeArg {
    String groupId;

    Set<String> describeApiNames;

    Integer permissionType;

    Set<String> sourceDeptIds;

    Set<String> sourceEmployeeIds;

    Set<String> sourceUserGroupIds;

    Set<String> sourceRoleIds;

    Set<String> targetDeptIds;

    Set<String> targetEmployeeIds;

    Set<String> targetUserGroupIds;

    Set<String> targetRoleIds;

    Integer basedType;

    Integer shareDeptCascade;

    //是否包含子部门 1 包含 0/null  不包含
    Integer receiveDeptCascade;

    Boolean returnList;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  class Result extends BasePrivilegeResult {
    private Map<Integer, List<EntitySharePojo>> result;
  }

}
