package com.facishare.paas.appframework.privilege.util;

/**
 * Created by luxin on 2018/1/29.
 */
public class ActionCodeConvertUtil {

    /**
     * 自定义对象ActionCode到funcCode的转换方法
     * @param objectApiName
     * @param actionCode
     * @return
     */
    public static String convert2FuncCode(String objectApiName, String actionCode) {
        if ("List".equals(actionCode)) {
            return objectApiName;
        }
        return objectApiName + "||" + actionCode;

    }

}
