package com.facishare.paas.appframework.privilege.util;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by shun on 2019/9/26
 */
public class AppIdUtil {

    public static String getAppId(User user) {
        return getAppId(user, RequestContextManager.getContext() == null ? null : RequestContextManager.getContext().getAppId());
    }

    public static String getAppId(User user, String appId) {
        if (StringUtils.isBlank(appId)) {
            return PrivilegeConstants.APP_ID;
        }
        return appId;
    }

    public static String getAppIdWithNoDefault() {
        return RequestContextManager.getContext() == null ? null : RequestContextManager.getContext().getAppId();
    }

}