package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;

/**
 * Created by zhouwr on 2017/10/23
 */
public enum ObjectDataPermissions {
    NO_PERMISSION("0", I18NKey.CONSTANT_NO_PERMISSION),
    PRIVATE("1", I18NKey.constant_private),
    PUBLIC_READONLY("2", I18NKey.constant_public_read),
    PUBLIC_READ_WRITE_DELETE("3", I18NKey.constant_public_read_write);

    private String value;
    private String labelKey;

    ObjectDataPermissions(String value, String labelKey) {
        this.value = value;
        this.labelKey = labelKey;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return I18N.text(labelKey);
    }
}
