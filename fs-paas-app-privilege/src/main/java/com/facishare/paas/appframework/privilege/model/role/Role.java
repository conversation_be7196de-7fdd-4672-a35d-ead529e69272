package com.facishare.paas.appframework.privilege.model.role;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;

import java.util.Objects;

/**
 * Created by luxin on 2018/5/28.
 */
public enum Role {

    CHANNEL_MANAGER("00000000000000000000000000000025", I18NKey.CHANNEL_MANAGER, I18NKey.CHANNEL_MANAGER_COMPANY, RoleType.DEFAULT_ROLE),
    AGENT("**********", I18NKey.AGENTS, I18NKey.DOWN_PRM_AGENTS, RoleType.DEFAULT_OUTER_ROLE),
    ACCOUNT_TRANSACTION_OPERATOR("00000000000000000000000000000027", I18NKey.CUSTOMER_TRANSACTION_OPERATOR, I18NK<PERSON>.DISTRIBUTION_CUSTOMER_TRANSACTION_PERMISSION, RoleType.DEFAULT_ROLE),
    MEMBER_MANAGER("00000000000000000000000000000028", I18N<PERSON>ey.MEMBER_MANAGER_ROLE, I18NKey.MEMBER_MANAGER_ROLE, RoleType.DEFAULT_ROLE),
    PURCHASING_AGENT("00000000000000000000000000000029", I18NKey.PURCHASING_AGENT, I18NKey.PURCHASING_AGENT_DESCRIPTION, RoleType.DEFAULT_ROLE),
    CHANNEL_STAFF("00000000000000000000000000000030", I18NKey.CHANNEL_STAFF, I18NKey.CHANNEL_STAFF_DESCRIPTION, RoleType.DEFAULT_ROLE);

    Role(String roleCode, String displayName, String description, RoleType type) {
        this.roleCode = roleCode;
        this.displayName = displayName;
        this.description = description;
        this.type = type;
    }

    private String roleCode;
    private String displayName;
    private String description;
    private RoleType type;

    public String getRoleCode() {
        return roleCode;
    }

    public String getDisplayName() {
        return I18N.text(displayName);
    }

    public String getDescription() {
        return I18N.text(description);
    }

    public static Role getRoleByRoleCode(String roleCode) {
        for (Role value : Role.values()) {
            if (Objects.equals(value.getRoleCode(), roleCode)) {
                return value;
            }
        }
        return null;
    }

    public RoleType getType() {
        return type;
    }


    public enum RoleType {
        DEFAULT_ROLE(1), USER_DEFINED_ROLE(2), DEFAULT_OUTER_ROLE(3), USER_DEFINED_OUTER_ROLE(4);

        RoleType(int type) {
            this.type = type;
        }

        public int getType() {
            return type;
        }

        private int type;
    }
}
