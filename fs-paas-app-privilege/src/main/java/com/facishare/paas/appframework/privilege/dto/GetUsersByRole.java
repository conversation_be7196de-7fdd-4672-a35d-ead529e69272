package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhouwr on 2017/10/16
 */
public interface GetUsersByRole {
    @Data
    class Arg {
        private AuthContext authContext;
        private String roleCode;
        private PageInfo pageInfo;
    }

    @Data
    class Result {
        private int errCode;
        private String errMessage;
        private RoleUserPageInfo result;
        private boolean success;
    }

    @Data
    class RoleUserPageInfo {
        private List<String> users = new ArrayList<String>();
        private PageInfo pageInfo;
    }
}
