package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentRightsResult {

    String id;
    String entityId;
    String deptId;
    long createdTime;
    long lastModifiedTime;
    String createdBy;
    String lastModifiedBy;
    boolean includeSub;
    Integer status;
    Integer scene;
    Integer type;


    public static DepartmentRightsResult of(DepartmentDataRights departmentDataRights){
        DepartmentRightsResult result = DepartmentRightsResult.builder()
                .id(departmentDataRights.getId())
                .entityId(departmentDataRights.getEntityId())
                .createdTime(departmentDataRights.getCreatedTime())
                .lastModifiedTime(departmentDataRights.getLastModifiedTime())
                .createdBy(departmentDataRights.getCreatedBy())
                .lastModifiedBy(departmentDataRights.getLastModifiedBy())
                .status(departmentDataRights.getStatus())
                .type(departmentDataRights.getType())
                .scene(ObjectUtils.isEmpty(departmentDataRights.getScene()) ? Integer.valueOf(0) : departmentDataRights.getScene())
                .deptId(departmentDataRights.getDeptId())
                .build();

        /**
         * includeSub 会被废弃
         */
        if (ObjectUtils.isEmpty(departmentDataRights.getType())){
            result.setIncludeSub(departmentDataRights.isIncludeSub());
        }else {
            if (Objects.equals(departmentDataRights.getType(),0)){
                result.setIncludeSub(true);
            }else {
                result.setIncludeSub(false);
            }
        }

        return result;
    }

}
