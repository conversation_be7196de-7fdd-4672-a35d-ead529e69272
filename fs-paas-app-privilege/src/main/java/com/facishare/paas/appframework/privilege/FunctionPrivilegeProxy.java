package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * Remote function privilege service proxy
 * <p>
 * Created by liyiguang on 2017/8/16.
 */
@RestResource(value = "PAAS-PRIVILEGE", desc = "功能权限服务", contentType = "application/json")
public interface FunctionPrivilegeProxy {

    @POST(value = "/funcPermissionCheck", desc = "功能权限检查")
    CheckFunctionPrivilege.Result checkFunctionPrivilege(@Body CheckFunctionPrivilege.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/queryFuncCodePrefixAndExact", desc = "根据精确code和前缀一起查询功能权限")
    QueryFuncCodePrefixAndExact.Result queryFuncCodePrefixAndExact(@Body QueryFuncCodePrefixAndExact.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/userFieldPermiss", desc = "获取字段权限")
    GetFieldsPermission.Result getFieldsPermission(@Body GetFieldsPermission.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/userEntitysFieldPermiss", desc = "获取字段权限")
    GetEntitiesFieldPermission.Result getEntitiesFieldPermission(@Body GetEntitiesFieldPermission.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/addFunc", desc = "创建功能权限")
    CreateFunctionPrivilege.Result createFunctionPrivilege(@Body CreateFunctionPrivilege.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/updateRoleModifiedFuncPermission", desc = "更新功能权限")
    UpdateRoleModifiedFuncPrivilege.Result updateRoleModifiedFuncPrivilege(@Body UpdateRoleModifiedFuncPrivilege.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/funcCodePermiss", desc = "查询具有目标funcCodes的权限的角色列表")
    FuncCodePrivilege.Result getHaveFuncCodesPrivilegeRoles(@Body FuncCodePrivilege.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/updateFuncRolePermission", desc = "批量更新多个角色的某个功能权限")
    UpdateFuncCodeRoles.Result updateFuncCodeRoles(@Body UpdateFuncCodeRoles.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/batchDelFunc", desc = "删除权限码")
    DelFuncCodeRoles.Result delFuncCodes(@Body DelFuncCodeRoles.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/roleInfo", desc = "获取角色列表")
    RoleListInfo.Result getRoleList(@Body RoleListInfo.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/updateFunc", desc = "更新func权限")
    UpdateFunc.Result updateFunc(@Body UpdateFunc.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/roleFuncPermiss", desc = "查询某个角色下的的权限")
    FuncPermiss.Result roleFuncPermiss(@Body FuncPermiss.RoleFuncPermissArg arg, @HeaderMap Map<String, String> header);

    @Deprecated
    @POST(value = "/rolesEntitysFieldPermiss", desc = "查询多个角色的权限合集")
    FuncPermiss.Result rolesEntitysFieldPermiss(@Body FuncPermiss.RolesFuncPermissArg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/updateRoleFuncPermiss", desc = "更新角色的功能权限，覆盖式更新")
    FuncPermiss.BaseResult updateRoleFuncPermiss(@Body FuncPermiss.UpdateRoleFuncPermissArg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/rolesEntitysFieldPermiss", desc = "查询多个角色的权限合集")
    FuncPermiss.UserEntitysFieldPermissResponse batchQueryRolesEntitysFieldPermiss(@Body FuncPermiss.RolesEntitysFieldPermissArg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/rolesFuncPermiss", desc = "查询多个角色的权限合集")
    FuncPermiss.PassBaseResult<Map<String, List<String>>> rolesFuncPermiss(@Body FuncPermiss.RolesFuncPermissArg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/copyObjFuncAccess", desc = "复制对象功能权限和字段权限")
    CopyObjFuncAndFieldPrivilege.Result copyObjFunc(@Body CopyObjFuncAndFieldPrivilege.Arg arg, @HeaderMap Map<String, String> header);
    class HeaderUtil {
        public static Map<String, String> buildHeaders(String ei) {
            Map<String, String> ret = Maps.newHashMap();
            ret.put("x-fs-ei", ei);
            return ret;
        }
    }
}
