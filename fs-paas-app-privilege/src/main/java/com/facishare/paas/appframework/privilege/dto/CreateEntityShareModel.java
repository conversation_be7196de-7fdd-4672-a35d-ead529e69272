package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by yusb on 2017/4/24.
 */
public class CreateEntityShareModel {

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends BasePrivilegeArg {
        private List<EntitySharePojo> shareList;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BasePrivilegeResult {
        private List<String> result;
    }
}
