package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

public class DeleteDimensionRuleGroupModel {

  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Arg extends BasePrivilegeArg {
    private Set<String> ruleCodes;
  }


  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Result extends BasePrivilegeResult {
  }

}
