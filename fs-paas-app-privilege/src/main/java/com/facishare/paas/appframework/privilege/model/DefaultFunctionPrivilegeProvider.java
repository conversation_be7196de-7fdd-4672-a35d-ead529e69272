package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/11/4
 */
@Component
public class DefaultFunctionPrivilegeProvider implements FunctionPrivilegeProvider {

    private static final List<String> DEFAULT_ACTION_CODES;
    private static final Map<String, List<String>> DEFAULT_CUSTOM_INIT_ROLE_ACTION_CODES;

    static {
        DEFAULT_ACTION_CODES = Collections.unmodifiableList(Lists.newArrayList(
                ObjectAction.VIEW_LIST.getActionCode(),
                ObjectAction.VIEW_DETAIL.getActionCode(),
                ObjectAction.CREATE.getActionCode(),
                ObjectAction.CLONE.getActionCode(),
                ObjectAction.UPDATE.getActionCode(),
                ObjectAction.RELATE.getActionCode(),
                ObjectAction.INVALID.getActionCode(),
                ObjectAction.RECOVER.getActionCode(),
                ObjectAction.DELETE.getActionCode(),
                ObjectAction.BATCH_IMPORT.getActionCode(),
                ObjectAction.BATCH_EXPORT.getActionCode(),
                ObjectAction.CHANGE_OWNER.getActionCode(),
                ObjectAction.EDIT_TEAM_MEMBER.getActionCode(),
                ObjectAction.START_BPM.getActionCode(),
                ObjectAction.VIEW_ENTIRE_BPM.getActionCode(),
                ObjectAction.STOP_BPM.getActionCode(),
                ObjectAction.CHANGE_BPM_APPROVER.getActionCode(),
                ObjectAction.PRINT.getActionCode(),
                ObjectAction.INTELLIGENTFORM.getActionCode(),
                ObjectAction.LOCK.getActionCode(),
                ObjectAction.UNLOCK.getActionCode(),
                ObjectAction.MODIFYLOG_RECOVER.getActionCode(),
                // 增加流程使用的三个功能权限
                ObjectAction.STAGE_MOVETO.getActionCode(),
                ObjectAction.CHANGE_STAGE_CANDIDATEIDS.getActionCode(),
                ObjectAction.STAGE_BACKTO.getActionCode(),
                ObjectAction.STAGE_REACTIVATION.getActionCode(),
                // 新增「图片及附件下载」功能权限
                ObjectAction.PICTURE_ANNEX_DOWNLOAD.getActionCode(),
                // APPROVAL 新加的功能权限
                ObjectAction.VIEW_APPROVAL_INSTANCE_LOG.getActionCode(),
                ObjectAction.VIEW_APPROVAL_CONFIG.getActionCode(),
                // BPM 新加的功能权限
                ObjectAction.VIEW_BPM_INSTANCE_LOG.getActionCode(),
                //840审批流新增功能权限
                ObjectAction.APPROVAL_AFTER_ERROR_RETRY.getActionCode(),
                ObjectAction.APPROVAL_AFTER_ERROR_IGNORE.getActionCode(),
                //840业务流新增功能权限
                ObjectAction.BPM_AFTER_ERROR_RETRY.getActionCode(),
                ObjectAction.BPM_AFTER_ERROR_IGNORE.getActionCode(),
                //860 流程新增更换审批流处理人功能权限
                ObjectAction.CHANGE_APPROVAL_CANDIDATE_IDS.getActionCode(),
                //930 流程新增审批流-等待节点_立即执行功能权限 和 业务流-等待节点_立即执行功能权限
                ObjectAction.APPROVAL_DELAY_TASK_EXECUTE.getActionCode(),
                ObjectAction.BPM_DELAY_TASK_EXECUTE.getActionCode()
        ));

        Map<String, List<String>> actionCodeMap = Maps.newHashMap();
        actionCodeMap.put(PrivilegeConstants.REPORT_ADMIN_ROLE_CODE,
                Collections.unmodifiableList(Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode())));
        DEFAULT_CUSTOM_INIT_ROLE_ACTION_CODES = Collections.unmodifiableMap(actionCodeMap);
    }

    @Override
    public String getApiName() {
        return "";
    }

    @Override
    public List<String> getSupportedActionCodes() {
        return DEFAULT_ACTION_CODES;
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        return DEFAULT_CUSTOM_INIT_ROLE_ACTION_CODES;
    }

}
