package com.facishare.paas.appframework.privilege.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/08/25
 */
public class CompositeSpecificationManager {
    private List<EntitySharePojo> list;

    private CompositeSpecificationManager(List<EntitySharePojo> list) {
        this.list = list;
    }

    public static CompositeSpecificationManager of(List<EntitySharePojo> list) {
        return new CompositeSpecificationManager(list);
    }

    public boolean contains(EntitySharePojo entitySharePojo) {
        Optional<EntitySharePojo> sharePojo = list.stream().filter(entityIdEquals(entitySharePojo)
                .and(shareTypeEquals(entitySharePojo))
                .and(shareIdEquals(entitySharePojo))
                .and(receiveIdEquals(entitySharePojo))
                .and(receiveTypeEquals(entitySharePojo))
                .and(receiveTenantEquals(entitySharePojo))
                .and(baseTypeEquals(entitySharePojo)))
                .findFirst();
        sharePojo.ifPresent(x -> entitySharePojo.setId(x.getId()));
        return sharePojo.isPresent();
    }

    private Predicate<EntitySharePojo> baseTypeEquals(EntitySharePojo entitySharePojo) {
        return x -> Objects.equals(x.getBasedType(), entitySharePojo.getBasedType());
    }

    private Predicate<EntitySharePojo> receiveTenantEquals(EntitySharePojo entitySharePojo) {
        return x -> Objects.equals(x.getReceiveId(), entitySharePojo.getReceiveId());
    }


    private Predicate<EntitySharePojo> receiveDeptCascadeEquals(EntitySharePojo entitySharePojo) {
        return x -> Objects.equals(x.getReceiveDeptCascade(), entitySharePojo.getReceiveDeptCascade());
    }

    private Predicate<EntitySharePojo> entityIdEquals(EntitySharePojo entitySharePojo) {
        return x -> Objects.equals(x.getEntityId(), entitySharePojo.getEntityId());
    }

    private Predicate<EntitySharePojo> shareIdEquals(EntitySharePojo entitySharePojo) {
        return x -> Objects.equals(x.getShareId(), entitySharePojo.getShareId());
    }

    private Predicate<EntitySharePojo> shareTypeEquals(EntitySharePojo entitySharePojo) {
        return x -> Objects.equals(x.getShareType(), entitySharePojo.getShareType());
    }

    private Predicate<EntitySharePojo> receiveIdEquals(EntitySharePojo entitySharePojo) {
        return x -> Objects.equals(x.getReceiveId(), entitySharePojo.getReceiveId());
    }

    private Predicate<EntitySharePojo> receiveTypeEquals(EntitySharePojo entitySharePojo) {
        return x -> Objects.equals(x.getReceiveType(), entitySharePojo.getReceiveType());
    }

}
