package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.core.model.User;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by luxin on 2018/4/24.
 */
public interface FieldPrivilegeService {

    /**
     * 获取多个对象的字段权限
     *
     * @param tenantId
     * @param userId
     * @param apiNames
     * @return
     * @Nullable
     */
    Map<String, Map<String, Integer>> listUserEntitiesFiledPrivilege(String tenantId, String userId, List<String> apiNames);

    /**
     * 获取对象的字段权限
     *
     * @param tenantId
     * @param userId
     * @param apiName
     * @return
     * @Nullable
     */
    @Deprecated
    Map<String, Integer> getUserFieldPrivilege(String tenantId, String userId, String apiName);

    /**
     * 获取对象的字段权限
     */
    Map<String, Integer> getUserFieldPrivilege(User user, String apiName);

    Map<String, Set<String>> getUserNoExportFieldPrivilege(User user, Set<String> objectApiNames);
}
