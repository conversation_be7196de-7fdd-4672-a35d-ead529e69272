package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.DataPrivilegeCalcProgress;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

@RestResource(
        value = "PAAS-DATA-AUTH",
        desc = "数据权限计算进度", // ignoreI18n
        contentType = "application/json"
)
public interface DataAuthProxy {
    @POST(value = "/worker/process/detail", desc = "数据权限计算进度")
    DataPrivilegeCalcProgress.Result calcProgress(@Body DataPrivilegeCalcProgress.Arg arg);
}
