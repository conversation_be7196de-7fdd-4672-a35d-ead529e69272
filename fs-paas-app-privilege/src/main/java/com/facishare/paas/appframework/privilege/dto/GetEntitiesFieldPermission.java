package com.facishare.paas.appframework.privilege.dto;

import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/11/27
 */
public interface GetEntitiesFieldPermission {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private List<String> entitys;
    }

    @Data
    class Result {
        private int errCode;
        private String errMessage;
        private Map<String, Map<String, Integer>> result = Maps.newHashMap();
        private boolean success;
    }

}
