package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.appframework.core.model.User;
import lombok.Data;

@Data
public class DepartmentDataRightsContext {
    String tenantId;
    String appId;
    String userId;
    private static final String CRM_APP_ID = "CRM";


    public static DepartmentDataRightsContext of(User user) {
        DepartmentDataRightsContext context = new DepartmentDataRightsContext();
        context.setTenantId(user.getTenantId());
        context.setUserId(user.getUserId());
        context.setAppId(CRM_APP_ID);
        return context;
    }
}
