package com.facishare.paas.appframework.privilege.model.role;

import lombok.experimental.Delegate;

import java.util.Map;
import java.util.Set;

/**
 * Created by luxin on 2018/5/28.
 */
public abstract class RoleProvider {
    @Delegate
    public abstract Role getRole();

    public abstract Map<String, Boolean> getFuncCodeAndEditableMapping();

    public Set<String> getHavePermissFuncCodes() {
        return getFuncCodeAndEditableMapping().keySet();
    }
}
