package com.facishare.paas.appframework.privilege.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by linqy on 2017/5/6.
 */
public interface UpdateEntityShareStatusModel {
    int ENABLE_STATUS = 1;
    int DISABLE_STATUS = 0;

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private List<String> shareIds;
        private Integer status;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
    }
}
