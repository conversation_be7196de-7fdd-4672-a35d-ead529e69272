package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

public interface UpdateEntitySharePermissionModel {

  @Data
  @EqualsAndHashCode(callSuper = true)
  class Arg extends BasePrivilegeArg {
    private Set<String> shareIds;
    private Integer permission;
  }


  @Data
  @EqualsAndHashCode(callSuper = true)
  class Result extends BasePrivilegeResult {
  }
}
