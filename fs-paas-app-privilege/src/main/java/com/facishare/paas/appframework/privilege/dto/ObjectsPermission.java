package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/10/23
 */
public interface ObjectsPermission {

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private String entityId;
        private List<String> objects;
        private Boolean cascadeDept;
        private Boolean cascadeSubordinates;
        private String roleType;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private Map<String, String> result;
    }
}
