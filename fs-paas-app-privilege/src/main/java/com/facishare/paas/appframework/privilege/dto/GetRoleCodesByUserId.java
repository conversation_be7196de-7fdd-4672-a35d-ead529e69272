package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by liyiguang on 2018/8/16.
 */
public interface GetRoleCodesByUserId {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private List<String> result;
    }
}
