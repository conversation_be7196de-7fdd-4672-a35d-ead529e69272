package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/2/26 18:41
 */
public interface QueryRoleInfoListByUsersModel {
    @Data
    @AllArgsConstructor
    class Arg {
        private AuthContext authContext;
        private List<String> users;
        private List<String> excludeRoles = new ArrayList<String>();
    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private Map<String, Map<String, List<UserRolePojo>>> result;
        private boolean success;
    }

    @Data
    class UserRolePojo {
        private String tenantId;
        private String appId;
        private String roleCode;
        private String roleName;
        private Integer orgType;
        private String orgId;
        private Boolean defaultRole;

        public boolean getDefaultRole() {
            return defaultRole == null ? false : defaultRole;
        }

    }

}
