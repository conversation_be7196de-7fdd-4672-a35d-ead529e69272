package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

public interface BatchDeleteTemporaryRights {

  @EqualsAndHashCode(callSuper = true)
  @Data
  class Arg extends BasePrivilegeArg {
    private Set<String> temporaryRightsIds;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  class Result extends BasePrivilegeResult {
    DeleteTemporaryRights.DeleteResult result;
  }

  @Data
  class DeleteResult {

  }

}
