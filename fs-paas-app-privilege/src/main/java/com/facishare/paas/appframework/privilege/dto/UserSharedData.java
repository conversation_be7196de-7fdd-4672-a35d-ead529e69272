package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/10/16
 */
public interface UserSharedData {
    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private String entityId;
        private Boolean deptConvertToUser;
        private Boolean userParentDeptCascade;
        private Boolean userDeputyDept;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private Map<String, List<String>> result;
    }
}
