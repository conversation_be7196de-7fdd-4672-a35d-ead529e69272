package com.facishare.paas.appframework.privilege.model;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/11/4
 */
public interface FunctionPrivilegeProvider {
    /**
     * 获取该provider支持的对象的apiName
     *
     * @return
     */
    String getApiName();

    /**
     * 获取所有支持的操作码
     *
     * @return
     */
    List<String> getSupportedActionCodes();

    /**
     * 获取自定义需要初始化的角色以及其有权限的操作码(CRM管理员是默认初始化的，这里不需要返回)
     *
     * @return key:角色code value:该角色有权限的操作码
     */
    Map<String, List<String>> getCustomInitRoleActionCodes();

    /**
     * CRM管理员是否默认初始化
     *
     * @return
     */
    default boolean isAdminInitByDefault() {
        return true;
    }
}
