package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface QueryEntityShareCount {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Arg {
        String tenantId;
        String appId;
        String userId;
    }


    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private Integer result;
        private Boolean success;
    }
}
