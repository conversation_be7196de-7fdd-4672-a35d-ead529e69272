package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouwr on 2017/11/4
 */
@Component
public class FunctionPrivilegeProviderManager implements ApplicationContextAware {

    private Map<String, FunctionPrivilegeProvider> providerMap = Maps.newHashMap();

    @Autowired
    private DefaultFunctionPrivilegeProvider defaultFunctionPrivilegeProvider;
    @Autowired
    private FunctionPrivilegeProviderProxy proxy;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initFunctionPrivilegeProviderMap(applicationContext);
    }

    private void initFunctionPrivilegeProviderMap(ApplicationContext applicationContext) {
        Map<String, FunctionPrivilegeProvider> springBeanMap = applicationContext.getBeansOfType(FunctionPrivilegeProvider.class);
        springBeanMap.values().forEach(provider -> {
            if (StringUtils.isNotEmpty(provider.getApiName())) {
                providerMap.put(provider.getApiName(), provider);
            }
        });
    }

    public FunctionPrivilegeProvider getProvider(String apiName) {
        if (ObjectDescribeExt.isCustomObject(apiName)) {
            return defaultFunctionPrivilegeProvider;
        }
        FunctionPrivilegeProvider provider = providerMap.get(apiName);
        if (provider != null) {
            return provider;
        }
        return getRemoteProvider(apiName);
    }

    public FunctionPrivilegeProvider getLocalProvider(String apiName) {
        return providerMap.getOrDefault(apiName, defaultFunctionPrivilegeProvider);
    }

    private FunctionPrivilegeProvider getRemoteProvider(String apiName) {
        return RemoteFunctionPrivilegeProvider.builder()
                .describeApiName(apiName)
                .proxy(proxy)
                .build();
    }
}
