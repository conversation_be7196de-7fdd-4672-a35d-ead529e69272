package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.privilege.dto.AddRoleModel;
import com.facishare.paas.appframework.privilege.dto.RoleInfoListByTypesModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Created by luxin on 2018/5/28.
 */
@RestResource(value = "PAAS-PRIVILEGE", desc = "功能权限服务-角色操作", contentType = "application/json")
public interface RoleProxy {

    @POST(value = "/addRole", desc = "增加角色")
    AddRoleModel.Result addRole(@Body AddRoleModel.Arg arg, @HeaderMap Map<String, String> header);


    @POST(value = "/roleInfo/roleTypes", desc = "角色查询")
    RoleInfoListByTypesModel.Result roleInfoListByRoleTypes(@Body RoleInfoListByTypesModel.Arg arg, @HeaderMap Map<String, String> header);
}
