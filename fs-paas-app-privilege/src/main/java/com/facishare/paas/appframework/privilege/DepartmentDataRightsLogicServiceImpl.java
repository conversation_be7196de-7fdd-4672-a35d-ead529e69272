package com.facishare.paas.appframework.privilege;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.privilege.dto.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class DepartmentDataRightsLogicServiceImpl implements DepartmentDataRightsLogicService {
    @Autowired
    private DepartmentDataRightsProxy rightsProxy;

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    @Autowired
    private DataPrivilegeCommonService dataPrivilegeCommonService;

    @Autowired
    private ManageGroupService manageGroupService;

    private static final int ENABLE_STATUS = 0;
    private static final int DISABLE_STATUS = 1;

    @Override
    public void upsert(User user, List<String> objectAPINames, List<String> departmentIds, int scene, int type) {
        if (ObjectUtils.isEmpty(objectAPINames) || ObjectUtils.isEmpty(departmentIds)) {
            return;
        }

        //取笛卡尔积
        List<DepartmentDataRightsUpsert.DepartmentRights> rightsList = new ArrayList<>();
        for (int apiNameIndex = 0; apiNameIndex < objectAPINames.size(); apiNameIndex++) {
            for (int deptIndex = 0; deptIndex < departmentIds.size(); deptIndex++) {
                DepartmentDataRightsUpsert.DepartmentRights departmentRights = DepartmentDataRightsUpsert.DepartmentRights.builder()
                        .status(ENABLE_STATUS)
                        .type(type)
                        .entityId(objectAPINames.get(apiNameIndex))
                        .deptId(departmentIds.get(deptIndex))
                        .scene(scene)
                        .build();
                rightsList.add(departmentRights);
            }
        }

        DepartmentDataRightsContext context = DepartmentDataRightsContext.of(user);
        DepartmentDataRightsUpsert.Arg arg = DepartmentDataRightsUpsert.Arg.builder()
                .context(context)
                .deptRights(rightsList)
                .build();
        Map<String, String> header = RestUtils.buildHeaders(user);
        rightsProxy.upsert(header, arg);
    }

    @Override
    public Tuple<PageInfo, List<DepartmentDataRights>> query(User user, List<String> objectAPINames, List<String> departmentIds, int scene, int page, int size) {
        page = page <= 0 ? 1 : page;
        size = size <= 0 ? 20 : (size >= 200 ? 200 : size);

        if (dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(user.getTenantId()) && !user.isOutUser()) {
            //查询分管的对象
            ManageGroup manageGroup = manageGroupService.queryManageGroup(user, null, ManageGroupType.OBJECT, true);
            if (manageGroup == null) {
                return Tuple.of(dataPrivilegeCommonService.getDefaultPageInfo(page, size), Lists.newArrayList());
            }
            if (!manageGroup.isAllSupport()) {
                objectAPINames = dataPrivilegeCommonService.checkEntityIdsByManageGroup(objectAPINames, manageGroup);
                if (CollectionUtils.isEmpty(objectAPINames)){
                    return Tuple.of(dataPrivilegeCommonService.getDefaultPageInfo(page, size), Lists.newArrayList());
                }
            }

            //查询分管的范围
            Set<String> deptAndOrgIds = functionPrivilegeService.queryDeptAndOrgIdsByScope(user);
            if (CollectionUtils.isEmpty(deptAndOrgIds)) {
                return Tuple.of(dataPrivilegeCommonService.getDefaultPageInfo(page, size), Lists.newArrayList());
            }
            if (!deptAndOrgIds.contains("999999")) {
                departmentIds = Lists.newArrayList(dataPrivilegeCommonService.checkDeptAndOrgIdsByManageScope(departmentIds, deptAndOrgIds));
                if (CollectionUtils.isEmpty(departmentIds)){
                    return Tuple.of(dataPrivilegeCommonService.getDefaultPageInfo(page, size), Lists.newArrayList());
                }
            }
        }

        DepartmentDataRightsContext context = DepartmentDataRightsContext.of(user);
        DepartmentDataRightsQueryAll.Arg arg = DepartmentDataRightsQueryAll.Arg.builder().context(context)
                .currentPage(page)
                .size(size)
                .deptIds(departmentIds)
                .scene(scene)
                .entityIds(objectAPINames)
                .build();
        Map<String, String> header = RestUtils.buildHeaders(user);
        DepartmentDataRightsQueryAll.Result result = rightsProxy.queryAll(header, arg);
        return Tuple.of(result.getPageInfo(), result.getDeptRights());
    }

    @Override
    public List<DepartmentDataRights> findByIds(User user, int scene, List<String> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        DepartmentDataRightsContext context = DepartmentDataRightsContext.of(user);
        return findByIds(context, scene, ids);
    }

    @Override
    public void disableByIds(User user, int scene, List<String> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }

        DepartmentDataRightsContext context = DepartmentDataRightsContext.of(user);
        List<DepartmentDataRights> findResult = findByIds(context, scene, ids);
        if (ObjectUtils.isEmpty(findResult)) {
            return;
        }

        List<DepartmentDataRightsUpsert.DepartmentRights> upsertList = findResult.stream().map(departmentDataRights -> {

            DepartmentDataRightsUpsert.DepartmentRights result = DepartmentDataRightsUpsert.DepartmentRights.builder()
                    .deptId(departmentDataRights.getDeptId())
                    .entityId(departmentDataRights.getEntityId())
                    .status(DISABLE_STATUS)
                    .type(departmentDataRights.getType())
                    .scene(ObjectUtils.isEmpty(departmentDataRights.getScene()) ? 0 : departmentDataRights.getScene())
                    .build();

            Integer type = null;
            if (ObjectUtils.isEmpty(departmentDataRights.getType())) {
                type = departmentDataRights.isIncludeSub() ? 0 : 1;
            } else {
                type = departmentDataRights.getType();
            }
            result.setType(type);
            return result;
        }).collect(Collectors.toList());

        upsert(context, upsertList);
    }

    @Override
    public void deleteByIds(User user, int scene, List<String> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }

        DepartmentDataRightsContext context = DepartmentDataRightsContext.of(user);
        List<DepartmentDataRights> queryResult = findByIds(context, scene, ids);
        if (ObjectUtils.isEmpty(queryResult)) {
            return;
        }

        //启用状态不能删除
        boolean errorStatus = queryResult.stream().anyMatch(departmentDataRights ->
                Objects.equals(departmentDataRights.getStatus(), ENABLE_STATUS));
        if (errorStatus){
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }

        Map<String,List<String>> datas = new HashMap<>();
        queryResult.stream().collect(Collectors.groupingBy(DepartmentDataRights::getEntityId))
                .forEach((key,value) ->{
                    List<String> deleteId = value.stream()
                            .map(DepartmentDataRights::getId)
                            .collect(Collectors.toList());
                    datas.put(key,deleteId);
                });


        DepartmentDataRightsDelete.Arg arg = DepartmentDataRightsDelete.Arg.builder()
                .context(context)
                .scene(scene)
                .datas(datas)
                .build();
        Map<String, String> header = RestUtils.buildHeaders(user);
        rightsProxy.delete(header, arg);
    }

    @Override
    public void enable(User user, int scene, List<String> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }

        DepartmentDataRightsContext context = DepartmentDataRightsContext.of(user);
        List<DepartmentDataRights> findResult = findByIds(context, scene, ids);
        if (ObjectUtils.isEmpty(findResult)) {
            return;
        }

        List<DepartmentDataRightsUpsert.DepartmentRights> upsertList = findResult.stream().map(departmentDataRights ->
                DepartmentDataRightsUpsert.DepartmentRights.builder()
                        .deptId(departmentDataRights.getDeptId())
                        .entityId(departmentDataRights.getEntityId())
                        .status(ENABLE_STATUS)
                        .type(departmentDataRights.getType())
                        .scene(ObjectUtils.isEmpty(departmentDataRights.getScene()) ? 0 : departmentDataRights.getScene())
                        .build()
        ).collect(Collectors.toList());

        upsert(context, upsertList);
    }


    private void upsert(DepartmentDataRightsContext context, List<DepartmentDataRightsUpsert.DepartmentRights> rightsList) {
        DepartmentDataRightsUpsert.Arg arg = DepartmentDataRightsUpsert.Arg
                .builder()
                .context(context)
                .deptRights(rightsList)
                .build();
        User user = User.builder().tenantId(context.getTenantId()).build();
        Map<String, String> header = RestUtils.buildHeaders(user);
        rightsProxy.upsert(header, arg);
    }


    private List<DepartmentDataRights> findByIds(DepartmentDataRightsContext context, int scene, List<String> ids) {
        DepartmentDataRightsQueryByIds.Arg arg = DepartmentDataRightsQueryByIds.Arg.builder()
                .context(context)
                .ids(ids)
                .scene(scene)
                .build();
        User user = User.builder().tenantId(context.getTenantId()).build();
        Map<String, String> header = RestUtils.buildHeaders(user);
        List<DepartmentDataRights> result = rightsProxy.queryByIds(header, arg);
        return result;
    }
}
