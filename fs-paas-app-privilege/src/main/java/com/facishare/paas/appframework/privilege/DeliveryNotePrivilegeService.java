package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.FuncCodePrivilege;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by luxin on 2018/3/7.
 */
@Service
@Slf4j
public class DeliveryNotePrivilegeService {
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private FunctionPrivilegeProxy privilegeProxy;


    /**
     * 发货单对象初始化完成后,通过与销售订单对象的关联关系 给发货单对象 增加权限
     *
     * @param tenantId
     * @param userId
     */
    public void deliveryNoteAddFuncAccess(String tenantId, String userId) {
        FuncCodePrivilege.Arg arg = new FuncCodePrivilege.Arg(buildAuthContext(tenantId, userId),
                Lists.newArrayList(PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE, PrivilegeConstants.SALES_ORDER_CONFIRM_RECEIPT_FUNC_CODE));
        FuncCodePrivilege.Result result = privilegeProxy.getHaveFuncCodesPrivilegeRoles(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(tenantId));

        if (result.isSuccess()) {
            User user = new User(tenantId, userId);
            result.getResult().forEach((funcCode, roleCodes) -> {
                if (CollectionUtils.notEmpty(roleCodes)) {
                    try {
                        if (PrivilegeConstants.SALES_ORDER_CONFIRM_DELIVERY_FUNC_CODE.equals(funcCode)) {
                            functionPrivilegeService.rolesAddFuncAccess(user, "DeliveryNoteObj", "Add", roleCodes);
                        } else {
                            functionPrivilegeService.rolesAddFuncAccess(user, "DeliveryNoteObj", "ConfirmReceipt", roleCodes);
                        }
                    } catch (Exception e) {
                        log.error("deliveryNoteAddFuncAccess rolesAddFuncAccess error. tenantId {},userId {}", tenantId, userId, e);
                    }
                }
            });
        } else {
            log.error("getHaveFuncCodesPrivilegeRoles error,arg:{},result:{}", arg, result);
        }
    }


    private AuthContext buildAuthContext(String tenantId, String userId) {
        return AuthContext.builder()
                .appId(PrivilegeConstants.APP_ID)
                .tenantId(tenantId)
                .userId(userId)
                .build();
    }

}
