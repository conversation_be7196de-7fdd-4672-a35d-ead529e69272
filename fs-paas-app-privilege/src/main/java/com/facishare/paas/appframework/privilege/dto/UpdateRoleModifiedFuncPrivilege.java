package com.facishare.paas.appframework.privilege.dto;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2017/11/1
 */
public interface UpdateRoleModifiedFuncPrivilege {
    @Data
    @Builder
    @AllArgsConstructor
    class Arg {
        private AuthContext authContext;
        private String roleCode;
        private List<String> addFuncCode = Lists.newArrayList();
        private List<String> delFuncCode = Lists.newArrayList();

        /**
         * 对象展示名称
         */
        private String displayName;

        public Arg(AuthContext authContext, String roleCode, List<String> addFuncCode, List<String> delFuncCode) {
            this(authContext, roleCode, addFuncCode, delFuncCode, null);
        }
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Object result;
        private boolean success;
    }

}
