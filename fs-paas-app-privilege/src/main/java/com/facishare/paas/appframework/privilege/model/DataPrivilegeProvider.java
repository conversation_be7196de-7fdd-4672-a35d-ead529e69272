package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

public interface DataPrivilegeProvider {
    /**
     * 获取该provider支持的对象的apiName
     *
     * @return
     */
    String getApiName();

    /**
     * 批量校验数据业务权限
     * @param dataPrivilegeMap 通用数据权限map
     * @param dataList
     * @param actionCodes
     * @return
     */
    Map<String,Map<String, Permissions>> checkBusinessPrivilege(User user, Map<String, Permissions> dataPrivilegeMap,
                                                    List<IObjectData> dataList,
                                                    List<String> actionCodes);

    /**
     * 校验数据业务权限
     * @param dataPrivilegeMap 通用数据权限map
     * @param dataList
     * @param actionCode
     * @return
     */
    Map<String, Permissions> checkBusinessPrivilege(User user, Map<String, Permissions> dataPrivilegeMap,
                                                                List<IObjectData> dataList,
                                                                String actionCode);
}
