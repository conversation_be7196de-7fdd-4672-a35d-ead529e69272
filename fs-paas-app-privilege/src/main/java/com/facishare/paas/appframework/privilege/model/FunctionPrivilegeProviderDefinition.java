package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import lombok.*;

import java.util.List;
import java.util.Map;

public interface FunctionPrivilegeProviderDefinition {

    interface GetSupportedActionCodes {
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        class Arg {
            String describeApiName;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        class Result {
            List<String> supportedActionCodes;
        }

        @Data
        @ToString(callSuper = true)
        @EqualsAndHashCode(callSuper = true)
        class RestResult extends BaseAPIResult {
            Result data;
        }
    }

    interface GetCustomInitRoleActionCodes {
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        class Arg {
            String describeApiName;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        class Result {
            Map<String, List<String>> customInitRoleActionCodes;
        }

        @Data
        @ToString(callSuper = true)
        @EqualsAndHashCode(callSuper = true)
        class RestResult extends BaseAPIResult {
            Result data;
        }
    }

    interface IsAdminInitByDefault {
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        class Arg {
            String describeApiName;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        class Result {
            Boolean isAdminInitByDefault;
        }

        @Data
        @ToString(callSuper = true)
        @EqualsAndHashCode(callSuper = true)
        class RestResult extends BaseAPIResult {
            Result data;
        }
    }

}
