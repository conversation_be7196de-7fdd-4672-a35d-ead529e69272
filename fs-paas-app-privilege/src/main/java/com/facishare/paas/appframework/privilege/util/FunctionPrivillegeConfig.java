package com.facishare.paas.appframework.privilege.util;

import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by shun on 2019/11/18
 *
 * <AUTHOR>
 */

public class FunctionPrivillegeConfig {

    @Getter
    private static List<String> slaveNoUseFunctionCodes = Lists.newArrayList("Clone", "Recover", "ChangeOwner", "Delete", "EditTeamMember", "Lock", "Unlock");
    @Getter
    private static Set<String> personnelRoleEditableFunctionCodes = Sets.newHashSet("ActiveRecordObj||Add", "ActiveRecordObj||Abolish");
    @Getter
    private static Map<String, MasterSlaveFunction> slaveMap = Maps.newHashMap();
    private static Set<String> personnelRoleEditableFunctionCodesGrayEnterpriseIds = Sets.newHashSet();
    private static Set<String> oldMasterDetailObjApiNamesEnterpriseIdSet = Sets.newHashSet();
    private static Set<String> slaveUseMasterFunctionEnterpriseIdSet = Sets.newHashSet();
    private static List<MasterSlaveFunction> masterCreateWithSlaveFunctionList = Lists.newArrayList();
    @Getter
    private static Set<String> crmUserListGrayEnterpriseIds;

    static {
        ConfigFactory.getInstance().getConfig("function-privillege-config", (config) -> {
            String oldMasterDetailObjApiNamesEnterpriseidString = config.get("old_master_detail_obj_api_names_enterpriseids");
            if (StringUtils.isNotBlank(oldMasterDetailObjApiNamesEnterpriseidString)) {
                oldMasterDetailObjApiNamesEnterpriseIdSet = Arrays.stream(oldMasterDetailObjApiNamesEnterpriseidString.split(",")).collect(Collectors.toSet());
            }

            String slaveUseMasterFunctionEnterpriseIdString = config.get("slaveUseMasterFunctionEnterpriseIds");
            if (StringUtils.isNotBlank(slaveUseMasterFunctionEnterpriseIdString)) {
                slaveUseMasterFunctionEnterpriseIdSet = Arrays.stream(slaveUseMasterFunctionEnterpriseIdString.split(",")).collect(Collectors.toSet());
            }

            String slaveNoUseFunctionCodesString = config.get("slaveNoUseFunctionCodes");
            if (StringUtils.isNotBlank(slaveNoUseFunctionCodesString)) {
                slaveNoUseFunctionCodes = Arrays.stream(slaveNoUseFunctionCodesString.split(",")).collect(Collectors.toList());
            }

            String personnelRoleEditableFunctionCodesString = config.get("personnelRoleEditableFunctionCodes");
            if (StringUtils.isNotBlank(personnelRoleEditableFunctionCodesString)) {
                personnelRoleEditableFunctionCodes = Arrays.stream(personnelRoleEditableFunctionCodesString.split(",")).collect(Collectors.toSet());
            }

            String personnelRoleEditableFunctionCodesGrayEnterpriseIdsString = config.get("personnelRoleEditableFunctionCodesGrayEnterpriseIds");
            if (StringUtils.isNotBlank(personnelRoleEditableFunctionCodesGrayEnterpriseIdsString)) {
                personnelRoleEditableFunctionCodesGrayEnterpriseIds = Arrays.stream(personnelRoleEditableFunctionCodesGrayEnterpriseIdsString.split(",")).collect(Collectors.toSet());
            }

            crmUserListGrayEnterpriseIds = Arrays.stream(config.get("crmUserListGrayEnterpriseIds", "").split(",")).collect(Collectors.toSet());

        });

        ConfigFactory.getInstance().getConfig("function-privillege-master-slave-config", (config) -> {
            String masterSlaveString = config.getString();
            if (StringUtils.isNotBlank(masterSlaveString)) {
                masterCreateWithSlaveFunctionList = JSON.parseArray(masterSlaveString, MasterSlaveFunction.class);
                slaveMap = masterCreateWithSlaveFunctionList.stream().collect(Collectors.toMap(MasterSlaveFunction::getSlaveApiName, Function.identity()));
            }
        });
    }

    public static boolean openGrayOld_master_detail_obj_api_names_enterpriseids(String enterpriseId) {
        if (oldMasterDetailObjApiNamesEnterpriseIdSet.contains("*")) {
            return true;
        }
        return oldMasterDetailObjApiNamesEnterpriseIdSet.contains(enterpriseId);
    }

    public static boolean openSlaveUseMasterFunction(String enterpriseId) {
        if (slaveUseMasterFunctionEnterpriseIdSet.contains("*")) {
            return true;
        }
        return slaveUseMasterFunctionEnterpriseIdSet.contains(enterpriseId);
    }

    public static boolean openPersonnelRoleEditableFunctionCodesGray(String enterpriseId) {
        if (personnelRoleEditableFunctionCodesGrayEnterpriseIds.contains("*")) {
            return true;
        }
        return personnelRoleEditableFunctionCodesGrayEnterpriseIds.contains(enterpriseId);
    }

}
