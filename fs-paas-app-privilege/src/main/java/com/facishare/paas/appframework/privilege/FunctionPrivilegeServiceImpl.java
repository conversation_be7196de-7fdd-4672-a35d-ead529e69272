package com.facishare.paas.appframework.privilege;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege.FunctionPojo;
import com.facishare.paas.appframework.privilege.metadatahandle.ObjectDisplayNameHandle;
import com.facishare.paas.appframework.privilege.model.FunctionCodeBuilder;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderManager;
import com.facishare.paas.appframework.privilege.util.*;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.RolePojo;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.paas.auth.factory.FieldClient;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy.HeaderUtil.buildHeaders;
import static com.facishare.paas.appframework.privilege.util.PrivilegeConstants.SUFFIX_OBJECT_LEVEL_VIEW_ALL;

/**
 * 封装权限服务接口
 * <p>
 * Created by liyiguang on 2017/6/21.
 */
@Slf4j
@Service("functionPrivilegeService")
public class FunctionPrivilegeServiceImpl implements FunctionPrivilegeService {

    private final static Map<String, String> ROLE_GROUP_MAPPING;

    private static final List<String> BIG_OBJECT_DEFAULT_ACTION_CODES;

    static {
        Map<String, String> tmp = new HashMap<>();
        //基础版
        tmp.put(LicenseConstants.Versions.VERSION_BASIC, "roleGroup_A");
        //标准版
        tmp.put(LicenseConstants.Versions.VERSION_STANDARD, "roleGroup_OldB");
        //专业版
        tmp.put(LicenseConstants.Versions.VERSION_STANDARDPRO, "roleGroup_B");
        //旗舰版
        tmp.put(LicenseConstants.Versions.VERSION_ENTERPRISE, "roleGroup");
        //旗舰增强版
        tmp.put(LicenseConstants.Versions.VERSION_STRENGTHEN, "roleGroup_B_plus");
        ROLE_GROUP_MAPPING = Collections.unmodifiableMap(tmp);
        BIG_OBJECT_DEFAULT_ACTION_CODES = Collections.unmodifiableList(Lists.newArrayList(
                ObjectAction.VIEW_LIST.getActionCode(),
                ObjectAction.VIEW_DETAIL.getActionCode(),
                ObjectAction.CREATE.getActionCode(),
                ObjectAction.CLONE.getActionCode(),
                ObjectAction.UPDATE.getActionCode(),
                ObjectAction.RELATE.getActionCode(),
                ObjectAction.INVALID.getActionCode(),
                ObjectAction.RECOVER.getActionCode(),
                ObjectAction.DELETE.getActionCode(),
                ObjectAction.CHANGE_OWNER.getActionCode(),
                // 新增「图片及附件下载」功能权限
                ObjectAction.PICTURE_ANNEX_DOWNLOAD.getActionCode()
        ));
    }

    @Autowired
    private RoleClient roleClient;
    @Autowired
    private FieldClient fieldClient;
    @Autowired
    private FuncClient funcClient;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    private MasterSlaveFunctionPrivilegeService masterSlaveFunctionPrivilegeService;
    @Autowired
    private FunctionPrivilegeProviderManager providerManager;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private ObjectDisplayNameHandle objectDisplayNameHandle;
    private IChangeableConfig prop = ConfigFactory.getConfig(PermissionConigUtil.OLD_CRM_DESC);

    @Override
    public boolean funPrivilegeCheck(User user, String objectApiName, String actionCode) {
        Map<String, Boolean> checkResult = funPrivilegeCheck(user, objectApiName, Lists.newArrayList(actionCode));
        return checkResult.getOrDefault(actionCode, false);
    }

    @Override
    public Map<String, Boolean> funPrivilegeCheck(User user, String objectApiName, List<String> actionCodes) {
        Map<String, Map<String, Boolean>> checkResult = batchFunPrivilegeCheck(user,
                Lists.newArrayList(objectApiName), actionCodes);
        return checkResult.getOrDefault(objectApiName, Maps.newHashMap());
    }

    @Override
    public List<String> funPrivilegeCheck(User user, List<String> objectApiNames, String actionCode) {
        Map<String, Map<String, Boolean>> checkResult =
                batchFunPrivilegeCheck(user, Lists.newArrayList(objectApiNames), Lists.newArrayList(actionCode));
        return objectApiNames.stream().filter(it -> checkResult.get(it).getOrDefault(actionCode, false))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Map<String, Boolean>> batchFunPrivilegeCheck(User user, List<String> objectApiNames,
                                                                    List<String> actionCodes) {
        if (user.isSupperAdmin()) {
            return buildDefaultResult(objectApiNames, actionCodes);
        }

        Map<String, Map<String, Boolean>> result = Maps.newHashMap();

        if (FunctionPrivillegeConfig.openSlaveUseMasterFunction(user.getTenantId())) {
            slaveUseMasterFillResultWithWhiteList(result, objectApiNames, actionCodes);
            slaveUseMasterCheckFuncPrivilegeByProxy(result, user, objectApiNames, actionCodes);
        } else {
            fillResultWithWhiteList(result, objectApiNames, actionCodes);
            checkFuncPrivilegeByProxy(result, user, objectApiNames, actionCodes);
        }
        return result;
    }

    @Override
    public Map<String, Boolean> batchFuncCodePrivilegeCheck(User user, List<String> funcCodes) {
        CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(buildAuthContext(user), funcCodes);
        if (result.isSuccess()) {
            return result.getResult();
        } else {
            throw new PermissionError(result.getErrMessage(), result.getErrCode());
        }
    }

    @Override
    public PrivilegeResult batchObjectFuncPrivilegeCheck(RequestContext context, Map<String, List<String>> apiName2ActionCodes) {
        List<String> funcCodes = Lists.newArrayList();
        apiName2ActionCodes.forEach((apiName, actionCodes) -> {
            actionCodes.forEach(actionCode -> {
                funcCodes.add(ActionCodeConvertUtil.convert2FuncCode(apiName, actionCode));
            });
        });

        CheckFunctionPrivilege.Result paasResult = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(buildAuthContext(context.getUser(), context.getAppId()), funcCodes);
        if (paasResult.isSuccess()) {
            Map<String, Map<String, Boolean>> apiName2FuncPrivilegeInfo = Maps.newHashMap();
            Map<String, Boolean> funcCode2Status = paasResult.getResult();
            apiName2ActionCodes.forEach((apiName, actionCodes) -> {
                actionCodes.forEach(actionCode -> {
                    Boolean status = funcCode2Status.get(ActionCodeConvertUtil.convert2FuncCode(apiName, actionCode));
                    Map<String, Boolean> funcPrivilegeInfo = apiName2FuncPrivilegeInfo.get(apiName);
                    if (funcPrivilegeInfo == null) {
                        funcPrivilegeInfo = Maps.newHashMap();
                        funcPrivilegeInfo.put(actionCode, status);
                        apiName2FuncPrivilegeInfo.put(apiName, funcPrivilegeInfo);
                    } else {
                        funcPrivilegeInfo.put(actionCode, status);
                    }
                });
            });
            return PrivilegeResult.ofSuccess(apiName2FuncPrivilegeInfo);
        } else {
            return PrivilegeResult.ofFail(paasResult.getErrCode(), paasResult.getErrMessage());
        }
    }

    @Override
    public Set<String> queryDeptAndOrgIdsByScope(User user) {
        Set<String> deptAndOrgIds = funcClient.queryLimitScopeByUserId(com.facishare.paas.auth.model.AuthContext
                .builder()
                .appId("CRM")
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .build(), user.getUserId());

        if (CollectionUtils.isEmpty(deptAndOrgIds)) {
            return Sets.newHashSet();
        }

        return deptAndOrgIds;
    }

    @Override
    public void doFunPrivilegeCheck(User user, String objectApiName, List<String> actionCodes) {
        if (CollectionUtils.isEmpty(actionCodes)) {
            return;
        }
        if (user.isSupperAdmin()) {
            return;
        }
        if (PrivilegeConstants.FUNC_CHECK_WHITE_LIST_API_NAMES.contains(objectApiName)) {
            return;
        }

        Map<String, Boolean> funcPrivileges = funPrivilegeCheck(user, objectApiName, actionCodes);
        if (MapUtils.isEmpty(funcPrivileges)) {
            String displayName = objectDisplayNameHandle.findObjectDisplayName(user.getTenantId(), objectApiName);
            log.warn("No operation function permissions,user:{},objectApiName:{},actionCodes:{}",
                    user, objectApiName, actionCodes);
            throw new PermissionError(I18N.text(I18NKey.OBJECT_FUNCTION_PERMISSIONS,
                    displayName,
                    actionCodes.stream().map(actionCode -> getButtonName(actionCode)).collect(Collectors.joining(","))));
        }

        for (String actionCode : actionCodes) {
            Boolean hasFunction = funcPrivileges.get(actionCode);
            if (hasFunction == null || !hasFunction) {
                String displayName = objectDisplayNameHandle.findObjectDisplayName(user.getTenantId(), objectApiName);
                log.warn("No operation function permissions,user:{},objectApiName:{},actionCodes:{}",
                        user, objectApiName, actionCodes);
                throw new PermissionError(I18N.text(I18NKey.OBJECT_FUNCTION_PERMISSIONS,
                        displayName,
                        getButtonName(actionCode)));
            }
        }
    }

    private String getButtonName(String actionCode) {
        ObjectAction objectAction = ObjectAction.of(actionCode);
        if (ObjectAction.UNKNOWN_ACTION == objectAction) {
            return actionCode;
        }
        return I18NExt.getOrDefault(objectAction.getI18NKey(), actionCode);
    }

    @Override
    public boolean funDataViewPrivilegeCheck(User user, IObjectDescribe describe) {
        String apiName = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe()
                .map(MasterDetailFieldDescribe::getTargetApiName)
                .orElseGet(describe::getApiName);
        return funDataViewPrivilegeCheck(user, apiName);
    }

    @Override
    public boolean funDataViewPrivilegeCheck(User user, String objectApiName) {
        String objectViewAllKey = ActionCodeConvertUtil.convert2FuncCode(objectApiName, SUFFIX_OBJECT_LEVEL_VIEW_ALL);
        Map<String, Boolean> viewAllDataPrivilege = funDataPrivilegeCheck(user, objectApiName, Lists.newArrayList(PrivilegeConstants.SYSTEM_LEVEL_VIEW_ALL_DATA, objectViewAllKey));
        return BooleanUtils.isTrue(viewAllDataPrivilege.get(PrivilegeConstants.SYSTEM_LEVEL_VIEW_ALL_DATA)) || BooleanUtils.isTrue(viewAllDataPrivilege.get(objectViewAllKey));
    }

    @Override
    public Map<String, Boolean> funDataPrivilegeCheck(User user, String objectApiName, List<String> actionCodes) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EDIT_AND_VIEW_ALL_DATA_PRIVILEGE, user.getTenantId())) {
            return Maps.newHashMap();
        }
        if (CollectionUtils.isEmpty(actionCodes)) {
            return Maps.newHashMap();
        }
        if (user.isSupperAdmin()) {
            Map<String, Map<String, Boolean>> checkResult = buildDefaultResult(Lists.newArrayList(objectApiName), actionCodes);
            return checkResult.getOrDefault(objectApiName, Maps.newHashMap());
        }
        CheckFunctionPrivilege.Result checkFunctionDataPrivilege = masterSlaveFunctionPrivilegeService.checkFunctionDataPrivilege(buildAuthContext(user), actionCodes);
        if (checkFunctionDataPrivilege.isSuccess()) {
            return checkFunctionDataPrivilege.getResult();
        } else {
            throw new PermissionError(checkFunctionDataPrivilege.getErrMessage(), checkFunctionDataPrivilege.getErrCode());
        }
    }

    @Override
    public Set<String> getUnauthorizedFields(User user, String objectApiName) {
        return getFieldsByPermissions(user, objectApiName, Sets.newHashSet(Permissions.NO_PERMISSION.intValue()));
    }

    @Override
    public Set<String> getUnauthorizedFieldsByRole(User user, String roleCode, String objectApiName) {
        Map<String, Integer> fieldMap = getFieldsPermissionByRole(user, roleCode, objectApiName);

        return parseFieldsPermission(objectApiName, Sets.newHashSet(Permissions.NO_PERMISSION.intValue()), fieldMap);
    }

    private Map<String, Integer> getFieldsPermissionByRole(User user, String roleCode, String objectApiName) {
        //超级管理员所有字段默认读写权限
        if (user.isSupperAdmin()) {
            return null;
        }
        com.facishare.paas.auth.model.AuthContext context = buildAuthContext(user);
        try {
            return fieldClient.queryFieldPermissionByRole(context, roleCode, objectApiName);
        } catch (AuthException e) {
            log.warn("getFieldsPermissionByRole fail, roleCode:{}, objectApiName:{}, context:{}, ", roleCode, objectApiName, context, e);
            throw new PermissionError(e.getMessage(), e.getCode());
        }
    }

    @Override
    public Set<String> getReadonlyFields(User user, String objectApiName) {
        return getFieldsByPermissions(user, objectApiName, Sets.newHashSet(
                Permissions.NO_PERMISSION.intValue(), Permissions.READ_ONLY.intValue()));
    }

    @Override
    public Map<String, Set<String>> getUnauthorizedFields(User user, List<String> objectApiNames) {
        return getEntitiesFieldByPermissions(user, objectApiNames, Sets.newHashSet(Permissions.NO_PERMISSION.intValue()));
    }

    @Override
    public void initFunctionPrivilege(User user, String objectApiName) {
        AuthContext context = buildOldAuthContext(user);
        List<FunctionPojo> functionPojoList = createFunctionPrivilege(context, objectApiName, false);
        //初始化某些角色的功能权限
        initRoleFunctionPrivilege(objectApiName, context, functionPojoList, false, null);
    }

    @Override
    public void initFunctionPrivilege(User user, IObjectDescribe describe) {
        String objectApiName = describe.getApiName();
        AuthContext context = buildOldAuthContext(user);
        List<FunctionPojo> functionPojoList = createFunctionPrivilege(context, objectApiName, describe.isBigObject());
        //初始化某些角色的功能权限
        initRoleFunctionPrivilege(objectApiName, context, functionPojoList, describe.isSocialObject(), describe.getDisplayName());
    }

    @Override
    public void deleteFunctionPrivilege(User user, String objectApiName) {
        AuthContext context = buildOldAuthContext(user);
        FunctionPrivilegeProvider provider = providerManager.getProvider(objectApiName);

        //删除指定角色的功能权限
        Map<String, List<String>> roleActionCodes = provider.getCustomInitRoleActionCodes();
        if (MapUtils.isNotEmpty(roleActionCodes)) {
            roleActionCodes.forEach((x, y) -> {
                List<String> funcCodes = y.stream()
                        .map(actionCode -> FunctionCodeBuilder.build(objectApiName, actionCode))
                        .collect(Collectors.toList());
                delRoleFunctionPrivilege(context, x, funcCodes);
            });
        }

        List<String> actionCodes = provider.getSupportedActionCodes();
        if (CollectionUtils.isNotEmpty(actionCodes)) {
            List<String> funcCodes = actionCodes.stream()
                    .map(actionCode -> FunctionCodeBuilder.build(objectApiName, actionCode))
                    .collect(Collectors.toList());

            //删除管理员的功能权限
            delRoleFunctionPrivilege(context, PrivilegeConstants.ADMIN_ROLE_CODE, funcCodes);
            //删除该对象的功能权限
            batchDelFunc(user, funcCodes);
        }
    }

    private List<FunctionPojo> createFunctionPrivilege(AuthContext context, String objectApiName, boolean isBigObject) {
        List<FunctionPojo> functionPojoList = getUserDefinedFunctionPojoList(context.getTenantId(), objectApiName, isBigObject);
        if (CollectionUtils.isEmpty(functionPojoList)) {
            log.warn("ignore initFunctionPrivilege as supported action code not defined,objectApiName:{}", objectApiName);
            return new ArrayList<>();
        }

        CreateFunctionPrivilege.Arg arg = CreateFunctionPrivilege.Arg.builder()
                .authContext(context)
                .functionPojoList(functionPojoList)
                .build();

        CreateFunctionPrivilege.Result result = functionPrivilegeProxy.createFunctionPrivilege(arg, buildHeaders(context.getTenantId()));
        if (!result.isSuccess()) {
            log.warn("createFunctionPrivilege error,arg:{},result:{}", arg, result);
            //功能权限唯一标识重复表示已经初始化过了，不用阻断后续的处理流程
            if (result.getErrCode() != PrivilegeConstants.ErrorCode.FUNC_CODE_DULPLICATE) {
                throw new PermissionError(I18N.text(I18NKey.INIT_PERMISSION_FAIL), result.getErrCode());
            }
        }
        return functionPojoList;
    }

    @Override
    public void updateSfaFunctionPrivilege(User user, String objectApiName, String detailApiName, Map<String, String> actionCodes) {
        AuthContext context = buildOldAuthContext(user);
        List<FunctionPojo> functionPojoList;
        try {
            functionPojoList = createFunctionPrivilege(context, objectApiName, false);
        } catch (Exception ex) {
            log.info("Privilege exists.");
            functionPojoList = new ArrayList<>();
        }
        List<String> oldFunctions = new ArrayList<>(actionCodes.keySet());
        FuncCodePrivilege.Arg arg = new FuncCodePrivilege.Arg(context, oldFunctions);
        FuncCodePrivilege.Result result = functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(arg, buildHeaders(context.getTenantId()));
        if (null == result || 0 != result.getErrCode()) {
            log.error("Permission error, {}", result);
            int errCode = result == null ? AppFrameworkErrorCode.NO_PERMISSION.getCode() : result.getErrCode();
            throw new PermissionError(I18N.text(I18NKey.GET_FUNCTION_PERMISSION_FAIL), errCode);
        }
        if (null != result.getResult() && !result.getResult().isEmpty()) {
            Map<String, Set<String>> roleFunctions = new HashMap<>();

            for (Map.Entry<String, List<String>> functionRoles : result.getResult().entrySet()) {
                String function = functionRoles.getKey();
                List<String> roles = functionRoles.getValue();
                if (CollectionUtils.isEmpty(roles)) {
                    continue;
                }
                roles.forEach(
                        role -> {
                            Set<String> functions = roleFunctions.getOrDefault(role, new HashSet<>());
                            functions.add(FunctionCodeBuilder.build(objectApiName, actionCodes.getOrDefault(function, function)));
                            if (StringUtils.isNotBlank(detailApiName)) {
                                functions.add(FunctionCodeBuilder.build(detailApiName,
                                        actionCodes.getOrDefault(function, function)));
                            }
                            roleFunctions.put(role, functions);
                        }
                );
            }
            for (Map.Entry<String, Set<String>> roleFunction : roleFunctions.entrySet()) {
                if (!PrivilegeConstants.ADMIN_ROLE_CODE.equals(roleFunction.getKey())) {
                    addRoleFunctionPrivilege(context, roleFunction.getKey(),
                            new ArrayList<>(roleFunction.getValue()), null);
                }
            }

        }
        if (CollectionUtils.isNotEmpty(functionPojoList)) {
            addRoleFunctionPrivilege(context, PrivilegeConstants.ADMIN_ROLE_CODE,
                    functionPojoList.stream().map(FunctionPojo::getFuncCode)
                            .collect(Collectors.toList()), null);
        }
        batchDelFunc(user, oldFunctions);
    }

    @Override
    @Deprecated
    public List<Map<String, String>> getRoleList(User user) {
        List<RolePojo> roles;
        try {
            roles = roleClient.queryRole(buildAuthContext(user), null, null);
        } catch (AuthException e) {
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL), e.getCode());
        }

        String version = licenseService.getVersion(user.getTenantId());
        String versionMapping = ROLE_GROUP_MAPPING.getOrDefault(version, "roleGroup_A");
        String orderedConfig = prop.get(versionMapping);

        String[] configRoles = orderedConfig.split(";");
        Map<String, Map<String, String>> roleCodeIndex = Maps.newLinkedHashMap();

        List<Map<String, String>> orderedResult = Lists.newLinkedList();

        roles.forEach(role -> {
            Map<String, String> obj = Maps.newHashMap();
            obj.put("roleCode", role.getRoleCode());
            obj.put("roleName", role.getRoleName());
            obj.put("roleType", String.valueOf(role.getRoleType()));
            obj.put("description", role.getDescription());
            obj.put("roleOrder", role.getRoleOrder());
            roleCodeIndex.put(role.getRoleCode(), obj);
        });

        for (String roleInfo : configRoles) {
            String configRoleCode = roleInfo.split(",")[0];
            if (!configRoleCode.equals(PrivilegeConstants.ORDER_GOODS_ROLE_CODE)) {
                if (roleCodeIndex.get(configRoleCode) != null) {
                    orderedResult.add(roleCodeIndex.get(configRoleCode));
                }
            }
            roleCodeIndex.remove(configRoleCode);
        }
        orderedResult.addAll(roleCodeIndex.values());
        return orderedResult;
    }

    @Override
    public List<String> getAllRoleCodes(User user) {
        try {
            Set<String> roles = roleClient.queryRoleCodeByUserId(buildAuthContext(user));
            return roles.stream().filter(code -> !code.equals(PrivilegeConstants.ADMIN_ROLE_CODE)).collect(Collectors.toList());
        } catch (AuthException e) {
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL), e.getCode());
        }
    }

    @Override
    public void batchDelFunc(User user, List<String> funcSet) {
        if (CollectionUtils.isEmpty(funcSet)) {
            return;
        }
        AuthContext authContext = buildOldAuthContext(user);
        DelFuncCodeRoles.Arg arg = DelFuncCodeRoles.Arg.builder().authContext(authContext).funcSet(funcSet).build();
        DelFuncCodeRoles.Result result = functionPrivilegeProxy.delFuncCodes(arg, buildHeaders(user.getTenantId()));
        if (!result.isSuccess()) {
            log.warn("batchDelFunc error, arg: {}, result: {}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.BATCH_DELETE_PERMISSION_FAIL), result.getErrCode());
        }
    }

    @Override
    public List<FunctionPojo> batchCreateFunc(User user, String describeApiName, List<String> actionCodes) {
        List<FunctionPojo> functions = actionCodes.stream().map(
                code -> FunctionPojo.buildFunctionPojo(user.getTenantId(), describeApiName, code)
        ).collect(Collectors.toList());
        return batchCreateFunc(user, functions);
    }

    @Override
    public List<FunctionPojo> batchCreateFunc(User user, List<FunctionPojo> functions) {
        CreateFunctionPrivilege.Arg createFunctionPrivilegeArg = CreateFunctionPrivilege.Arg.builder()
                .authContext(buildOldAuthContext(user))
                .functionPojoList(functions)
                .build();
        CreateFunctionPrivilege.Result createFuncResult = functionPrivilegeProxy.createFunctionPrivilege(createFunctionPrivilegeArg, buildHeaders(user.getTenantId()));

        if (!createFuncResult.isSuccess()) {
            log.warn("createFunctionPrivilege error,arg:{},result:{}", createFunctionPrivilegeArg, createFuncResult);
            throw new PermissionError(I18N.text(I18NKey.INIT_PERMISSION_FAIL_REASON, createFuncResult.getErrMessage()));
        }
        return functions;
    }

    /**
     * 企业增加自定义按钮的功能权限
     */
    @Override
    public void createFuncCode(User user, String describeApiName, String buttonApiName, String buttonName) {
        batchCreateFunctionPrivilege(user, Lists.newArrayList(getFunctionPojo(user.getTenantId(), describeApiName, buttonApiName, buttonName)));
    }

    @Override
    public void batchCreateFuncCode(User user, List<IUdefButton> buttons) {
        if (CollectionUtils.isEmpty(buttons)) {
            return;
        }
        List<FunctionPojo> functionPojoList = buttons.stream()
                .map(button -> getFunctionPojo(user.getTenantId(), button.getDescribeApiName(), button.getApiName(), button.getLabel()))
                .collect(Collectors.toList());
        batchCreateFunctionPrivilege(user, functionPojoList);
    }

    private void batchCreateFunctionPrivilege(User user, List<FunctionPojo> functionPojoList) {
        CreateFunctionPrivilege.Arg createFunctionPrivilegeArg = CreateFunctionPrivilege.Arg.builder()
                .authContext(buildOldAuthContext(user))
                .functionPojoList(functionPojoList)
                .build();

        CreateFunctionPrivilege.Result createFuncResult = functionPrivilegeProxy.createFunctionPrivilege(createFunctionPrivilegeArg, buildHeaders(user.getTenantId()));
        if (!createFuncResult.isSuccess()) {
            log.warn("batchCreateFunctionPrivilege error,arg:{},result:{}", createFunctionPrivilegeArg, createFuncResult);
            throw new PermissionError(I18N.text(I18NKey.INIT_PERMISSION_FAIL_REASON, createFuncResult.getErrMessage()), createFuncResult.getErrCode());
        }
    }

    @Override
    public void rolesAddFuncAccess(User user, String objectApiName, String actionCode, List<String> addRoles) {
        UpdateFuncCodeRoles.Arg updateFuncCodeRolesArg = UpdateFuncCodeRoles.Arg.builder()
                .authContext(buildOldAuthContext(user))
                .funcCode(ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode))
                .addRoleSet(addRoles)
                .delRoleSet(Lists.newArrayList())
                .build();
        UpdateFuncCodeRoles.Result updateFuncCodeRolesResult = functionPrivilegeProxy.updateFuncCodeRoles(updateFuncCodeRolesArg, buildHeaders(user.getTenantId()));

        if (!updateFuncCodeRolesResult.isSuccess()) {
            log.warn("updateFuncCodeRoles error,arg:{},result:{}", updateFuncCodeRolesArg, updateFuncCodeRolesResult);
            throw new PermissionError(I18N.text(I18NKey.BATCH_ADD_ROLE_PERMISSION_FAIL_REASON,
                    updateFuncCodeRolesResult.getErrMessage()), updateFuncCodeRolesResult.getErrCode());
        }
    }

    @Override
    public void updateUserDefinedFuncAccess(User user, String roleCode, String objectApiName,
                                            List<String> addActionCodes, List<String> deleteActionCodes) {
        updateUserDefinedFuncAccess(user, roleCode, generateFuncCodes(objectApiName, addActionCodes), generateFuncCodes(objectApiName, deleteActionCodes));
    }

    @Override
    public void updateUserDefinedFuncAccess(User user, String roleCode, List<String> addFuncCodes, List<String> deleteFuncCodes) {
        UpdateRoleModifiedFuncPrivilege.Arg arg = UpdateRoleModifiedFuncPrivilege.Arg.builder()
                .authContext(buildOldAuthContext(user))
                .roleCode(roleCode)
                .addFuncCode(addFuncCodes)
                .delFuncCode(deleteFuncCodes).build();

        UpdateRoleModifiedFuncPrivilege.Result result = functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, buildHeaders(user.getTenantId()));
        if (!result.isSuccess()) {
            throw new PermissionError(I18N.text(I18NKey.UPDATE_ROLE_PERMISSION_FAIL_REASON, result.getErrMessage()), result.getErrCode());
        }
    }

    @Override
    public Map<String, List<String>> getHaveFuncCodesPrivilegeRoles(User user, List<String> funcCodes) {
        FuncCodePrivilege.Arg arg = FuncCodePrivilege.Arg.builder()
                .authContext(buildOldAuthContext(user))
                .funcCodes(Lists.newArrayList(CollectionUtils.emptyIfNull(funcCodes)))
                .build();

        FuncCodePrivilege.Result result = functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(arg, buildHeaders(user.getTenantId()));
        if (!result.isSuccess()) {
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_INFO_LIST_FAIL, result.getErrMessage()), result.getErrCode());
        }
        return result.getResult();
    }

    @Override
    public void updatePreDefinedFuncAccess(User user, String roleCode, List<String> addFuncCodes, List<String> deleteFuncCodes) {
        UpdateRoleModifiedFuncPrivilege.Arg arg = UpdateRoleModifiedFuncPrivilege.Arg.builder()
                .authContext(buildOldAuthContext(user))
                .roleCode(roleCode)
                .addFuncCode(addFuncCodes)
                .delFuncCode(deleteFuncCodes).build();

        UpdateRoleModifiedFuncPrivilege.Result result = functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, buildHeaders(user.getTenantId()));
        if (!result.isSuccess()) {
            throw new PermissionError(I18N.text(I18NKey.UPDATE_ROLE_PERMISSION_FAIL_REASON, result.getErrMessage()), result.getErrCode());
        }
    }

    @Override
    public void updatePreDefinedFuncAccess(User user, String appId, String roleCode, List<String> needAddPrivilegeFuncCodes, List<String> needDeletePrivilegeFuncCodes) {
        AuthContext context = buildOldAuthContext(user, appId);
        UpdateRoleModifiedFuncPrivilege.Arg arg = UpdateRoleModifiedFuncPrivilege.Arg.builder()
                .authContext(context)
                .roleCode(roleCode)
                .addFuncCode(needAddPrivilegeFuncCodes)
                .delFuncCode(needDeletePrivilegeFuncCodes).build();

        UpdateRoleModifiedFuncPrivilege.Result result = functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, buildHeaders(context.getTenantId()));
        if (!result.isSuccess()) {
            throw new PermissionError(I18N.text(I18NKey.UPDATE_ROLE_PERMISSION_FAIL_REASON, result.getErrMessage()), result.getErrCode());
        }
    }

    @Override
    public List<String> getHavePrivilegeRolesByActionCode(User user, String objectApiName, String actionCode) {
        String funcCode = ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode);
        FuncCodePrivilege.Arg arg = new FuncCodePrivilege.Arg(buildOldAuthContext(user), Lists.newArrayList(funcCode));
        FuncCodePrivilege.Result result = functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(arg, buildHeaders(user.getTenantId()));
        if (!result.isSuccess()) {
            log.warn("getHaveFuncCodesPrivilegeRoles error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL_IN_PERMISSION), result.getErrCode());
        }

        return result.getResult().get(funcCode);
    }

    @Override
    public Map<String, List<String>> getHavePrivilegeRolesByActionCodes(User user, String objectApiName, List<String> functionCodes) {
        List<String> funcCode = functionCodes.stream()
                .map(actionCode -> ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode)).collect(Collectors.toList());
        FuncCodePrivilege.Arg arg = new FuncCodePrivilege.Arg(buildOldAuthContext(user), Lists.newArrayList(funcCode));
        FuncCodePrivilege.Result result = functionPrivilegeProxy.getHaveFuncCodesPrivilegeRoles(arg, buildHeaders(user.getTenantId()));
        if (!result.isSuccess()) {
            log.warn("getHavePrivilegeRolesByActionCodes error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL_IN_PERMISSION), result.getErrCode());
        }

        return result.getResult();
    }

    @Override
    public void deleteUserDefinedActionCode(User user, String objectApiName, String actionCode) {
        DelFuncCodeRoles.Arg arg = DelFuncCodeRoles.Arg.builder()
                .authContext(buildOldAuthContext(user))
                .funcSet(Lists.newArrayList(ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode)))
                .build();

        DelFuncCodeRoles.Result result = functionPrivilegeProxy.delFuncCodes(arg, buildHeaders(user.getTenantId()));

        if (!result.isSuccess()) {
            log.warn("updateFuncCodeRoles error,arg:{},result:{}", arg, result);
            throw new PermissionError(I18N.text(I18NKey.DELETE_PERMISSION_FAIL), result.getErrCode());
        }
    }


    @NotNull
    private CreateFunctionPrivilege.FunctionPojo getFunctionPojo(String tenantId, String describeApiName, String buttonApiName, String buttonName) {
        int funcType;
        if (StringUtils.endsWith(buttonApiName, "__c")) {
            funcType = PrivilegeConstants.USER_DEFINED_FUNCTION_CODE_TYPE;
        } else {
            funcType = PrivilegeConstants.SYSTEM_DEFINED_FUNCTION_CODE_TYPE;
        }
        CreateFunctionPrivilege.FunctionPojo functionPojo = new CreateFunctionPrivilege.FunctionPojo();
        functionPojo.setAppId(PrivilegeConstants.APP_ID);
        functionPojo.setTenantId(tenantId);
        functionPojo.setFuncName(buttonName);
        functionPojo.setFuncType(funcType);
        functionPojo.setFuncCode(ActionCodeConvertUtil.convert2FuncCode(describeApiName, buttonApiName));
        functionPojo.setParentCode(PrivilegeConstants.PAAS_PARENT_CODE);
        return functionPojo;
    }

    private List<String> generateFuncCodes(String objectApiName, List<String> actionCodes) {
        List<String> funcCodes = Lists.newArrayList();
        if (CollectionUtils.isEmpty(actionCodes)) {
            return funcCodes;
        }

        actionCodes.forEach(actionCode -> funcCodes.add(ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode)));
        return funcCodes;
    }


    private void initRoleFunctionPrivilege(String objectApiName, AuthContext authContext, List<FunctionPojo> functionPojoList,
                                           boolean isSocialObject, String objectDisplayName) {
        FunctionPrivilegeProvider provider = providerManager.getProvider(objectApiName);
        if (isSocialObject) {
            // 协同自定义对象增加『所有人员』功能权限
            List<String> adminFuncCodes = functionPojoList.stream().map(FunctionPojo::getFuncCode).collect(Collectors.toList());
            adminFuncCodes.add(FunctionCodeBuilder.build(objectApiName, ObjectAction.VIEW_ALL.getActionCode()));
            log.info("social object addRoleFunctionPrivilege , current adminFuncCodes :{}", JsonUtil.toJson(adminFuncCodes));
            addRoleFunctionPrivilege(authContext, PrivilegeConstants.PERSONNEL_ROLE, adminFuncCodes, objectDisplayName);
        }
        //判断CRM管理员的功能权限是否需要默认初始化
        if (provider.isAdminInitByDefault()) {
            List<String> adminFuncCodes = functionPojoList.stream().map(FunctionPojo::getFuncCode).collect(Collectors.toList());
            log.info("addRoleFunctionPrivilege , current adminFuncCodes :{}", JsonUtil.toJson(adminFuncCodes));
            addRoleFunctionPrivilege(authContext, PrivilegeConstants.ADMIN_ROLE_CODE, adminFuncCodes, objectDisplayName);
        }

        //其他自定义需要初始化的角色的功能权限
        Map<String, List<String>> customInitRoleActionCodes = provider.getCustomInitRoleActionCodes();
        if (MapUtils.isEmpty(customInitRoleActionCodes)) {
            log.info("customInitRoleActionCodes is null");
            return;
        }

        customInitRoleActionCodes.forEach((roleCode, actionCodes) -> {
            List<String> funcCodes = actionCodes.stream()
                    .map(actionCode -> FunctionCodeBuilder.build(objectApiName, actionCode))
                    .collect(Collectors.toList());
            log.info("addRoleFunctionPrivilege , current funCode :{}, roleCode:{}", JsonUtil.toJson(funcCodes), JsonUtil.toJson(roleCode));
            addRoleFunctionPrivilege(authContext, roleCode, funcCodes, objectDisplayName);
        });
    }

    //获得需要初始化的自定对象的funcCode信息
    private List<FunctionPojo> getUserDefinedFunctionPojoList(String tenantId, String apiName, boolean isBigObject) {
        FunctionPrivilegeProvider provider = providerManager.getProvider(apiName);
        List<String> actionCodes = provider.getSupportedActionCodes();
        if (isBigObject) {
            return BIG_OBJECT_DEFAULT_ACTION_CODES.stream()
                    .map(actionCode -> FunctionPojo.buildFunctionPojo(tenantId, apiName, actionCode))
                    .collect(Collectors.toList());
        }
        return actionCodes.stream()
                .map(actionCode -> FunctionPojo.buildFunctionPojo(tenantId, apiName, actionCode))
                .collect(Collectors.toList());
    }

    private void addRoleFunctionPrivilege(AuthContext authContext, String roleCode, List<String> addFuncCodes, String objectDisplayName) {
        if (CollectionUtils.isEmpty(addFuncCodes)) {
            return;
        }
        UpdateRoleModifiedFuncPrivilege.Arg arg = UpdateRoleModifiedFuncPrivilege.Arg.builder()
                .authContext(authContext)
                .roleCode(roleCode)
                .addFuncCode(addFuncCodes)
                .displayName(objectDisplayName)
                .build();
        UpdateRoleModifiedFuncPrivilege.Result result = functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, buildHeaders(authContext.getTenantId()));
        if (!result.isSuccess()) {
            log.error("addFunctionPrivilege error,arg:{},result:{}", arg, result);
        }
    }

    private void delRoleFunctionPrivilege(AuthContext authContext, String roleCode, List<String> delFuncCodes) {
        if (CollectionUtils.isEmpty(delFuncCodes)) {
            return;
        }
        UpdateRoleModifiedFuncPrivilege.Arg arg = UpdateRoleModifiedFuncPrivilege.Arg.builder()
                .authContext(authContext)
                .roleCode(roleCode)
                .delFuncCode(delFuncCodes)
                .build();
        UpdateRoleModifiedFuncPrivilege.Result result = functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, buildHeaders(authContext.getTenantId()));
        if (!result.isSuccess()) {
            log.error("addFunctionPrivilege error,arg:{},result:{}", arg, result);
        }
    }

    private Map<String, Integer> getFieldsPermission(User user, String objectApiName) {
        //超级管理员所有字段默认读写权限
        if (user.isSupperAdmin()) {
            return null;
        }
        //缓存里有的话，直接返回缓存的数据
        Pair<Boolean, Optional<Map<String, Integer>>> cacheData = ContextCacheUtil.getFieldPermissionCache(user.getUserId(), objectApiName);
        if (Boolean.TRUE.equals(cacheData.getKey())) {
            return cacheData.getValue().orElse(null);
        }
        com.facishare.paas.auth.model.AuthContext context = buildAuthContext(user);
        try {
            Map<String, Integer> permission = fieldClient.userFieldPermission(context, objectApiName);
            //将字段权限缓存到context中
            ContextCacheUtil.cacheFieldPermission(user.getUserId(), objectApiName, permission);
            return permission;
        } catch (AuthException e) {
            log.error("getFieldsPermission error,arg:{}, ", context);
            throw new PermissionError(e.getMessage(), e.getCode());
        }
    }

    private Set<String> getFieldsByPermissions(User user, String objectApiName, Set<Integer> permissions) {
        Map<String, Integer> fieldMap = getFieldsPermission(user, objectApiName);

        if (Objects.equals(objectApiName, "PersonnelObj") && fieldMap != null) {
            fieldMap.put("user_id", Permissions.READ_ONLY.intValue());
        }
        return parseFieldsPermission(objectApiName, permissions, fieldMap);
    }

    private Map<String, Set<String>> getEntitiesFieldByPermissions(User user, List<String> objectApiNames, Set<Integer> permissions) {
        com.facishare.paas.auth.model.AuthContext authContext = buildAuthContext(user);
        Map<String, Map<String, Integer>> mappings;
        try {
            mappings = fieldClient.userEntityIdsFieldPermission(authContext, Sets.newHashSet(objectApiNames));
        } catch (AuthException e) {
            throw new PermissionError(I18N.text(I18NKey.PERMISSION_ERROR), e.getCode());
        }

        Map<String, Set<String>> fieldMap = Maps.newHashMap();
        objectApiNames.forEach(x -> fieldMap.put(x, parseFieldsPermission(x, permissions, mappings.get(x))));
        return fieldMap;
    }

    private Set<String> parseFieldsPermission(String objectApiName, Set<Integer> permissions, Map<String, Integer> fieldMap) {
        if (fieldMap == null) {
            //返回null表示所有字段默认读写权限
            return Sets.newHashSet();
        }
        Set<String> fields = Sets.newHashSet(fieldMap.keySet());
        return fields.stream().filter(field -> permissions.contains(fieldMap.get(field))).collect(Collectors.toSet());
    }

    private Map<String, Map<String, Boolean>> buildDefaultResult(List<String> objectApiNames, List<String> actionCodes) {
        Map<String, Map<String, Boolean>> result = Maps.newHashMap();
        objectApiNames.forEach(apiName -> {
            result.computeIfAbsent(apiName, key -> Maps.newHashMap());
            actionCodes.forEach(actionCode -> result.get(apiName).put(actionCode, true));
        });
        return result;
    }

    private void slaveUseMasterFillResultWithWhiteList(Map<String, Map<String, Boolean>> result, List<String> objectApiNames, List<String> actionCodes) {
        objectApiNames.forEach(apiName -> {
            result.computeIfAbsent(apiName, key -> Maps.newHashMap());
            if (isApiNameInWhiteListSlaveUseMaster(apiName)) {
                actionCodes.forEach(actionCode -> {
                    result.get(apiName).put(actionCode, true);
                });
            } else {
                actionCodes.forEach(actionCode -> {
                    if (isActionCodeInWhiteList(actionCode)) {
                        result.get(apiName).put(actionCode, true);
                    }
                });
            }
        });
    }

    private void fillResultWithWhiteList(Map<String, Map<String, Boolean>> result, List<String> objectApiNames,
                                         List<String> actionCodes) {
        objectApiNames.forEach(apiName -> {
            result.computeIfAbsent(apiName, key -> Maps.newHashMap());
            if (isApiNameInWhiteList(apiName)) {
                actionCodes.forEach(actionCode -> {
                    result.get(apiName).put(actionCode, true);
                });
            } else {
                actionCodes.forEach(actionCode -> {
                    if (isActionCodeInWhiteList(actionCode)) {
                        result.get(apiName).put(actionCode, true);
                    }
                });
            }
        });
    }

    private List<FuncCheckParam> buildFuncCheckParams(Map<String, Map<String, Boolean>> result, String objectApiName,
                                                      List<String> actionCodes) {
        return actionCodes.stream()
                .filter(x -> {
                    final Map<String, Boolean> whiteApiNameMap = result.get(objectApiName);
                    if (Objects.isNull(whiteApiNameMap)) {
                        return true;
                    }
                    return !whiteApiNameMap.containsKey(x);
                })
                .map(x -> FuncCheckParam.builder()
                        .apiName(objectApiName)
                        .actionCode(x)
                        .funcCode(FunctionCodeBuilder.build(objectApiName, x))
                        .build())
                .filter(x -> Objects.nonNull(x.getFuncCode()))
                .collect(Collectors.toList());
    }


    private void slaveUseMasterCheckFuncPrivilegeByProxy(Map<String, Map<String, Boolean>> result, User user,
                                                         List<String> objectApiNames, List<String> actionCodes) {
        StopWatch stopWatch = StopWatch.create("checkFuncPrivilegeByProxy");

        Set<String> originalCheckApiNameSet = Sets.newHashSet(objectApiNames);

        Set<String> waiteCheckApiNames = Sets.newHashSet(objectApiNames);
        //查看需校验的apiNames 中包含 使用主对象功能权限的，将主对象加入check list中
        for (String slaveApiName : FunctionPrivillegeConfig.getSlaveMap().keySet()) {
            if (waiteCheckApiNames.contains(slaveApiName)) {
                waiteCheckApiNames.add(FunctionPrivillegeConfig.getSlaveMap().get(slaveApiName).getMasterApiName());
            }
        }

        List<FuncCheckParam> funcCheckParams = Lists.newArrayList();
        for (String objectApiName : waiteCheckApiNames) {
            funcCheckParams.addAll(buildFuncCheckParams(result, objectApiName, actionCodes));
        }
        stopWatch.lap("buildFuncCheckParams");

        List<String> funcCodeList = funcCheckParams.stream().map(FuncCheckParam::getFuncCode).collect(Collectors.toList());
        CheckFunctionPrivilege.Result checkResult = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(buildAuthContext(user), funcCodeList);
        stopWatch.lap("checkFunctionPrivilege");
        if (!checkResult.isSuccess() || checkResult.getResult() == null) {
            log.warn("funPrivilegeCheck failed,tenantId:{},userId:{},funcCodeList:{},result:{}", user.getTenantId(),
                    user.getUserId(), funcCodeList, checkResult);
            return;
        }

        //将从对象补充到param中，后面解析查询结果需要用到
//        objectApiNames.forEach(objectApiName -> {
//            if (FunctionPrivillegeConfig.getSlaveMap().containsKey(objectApiName)) {
//                funcCheckParams.addAll(buildFuncCheckParams(result, objectApiName, actionCodes));
//            }
//        });

        for (FuncCheckParam funcCheckParam : funcCheckParams) {
            //原始校验的 apiname ,put result中
            String apiName = funcCheckParam.getApiName();
            if (originalCheckApiNameSet.contains(apiName)) {
                String checkFunctionCode = funcCheckParam.getFuncCode();
                if (FunctionPrivillegeConfig.getSlaveMap().keySet().contains(apiName)) {
                    //换取主对象的functionCode
                    MasterSlaveFunction masterSlaveFunction = FunctionPrivillegeConfig.getSlaveMap().get(apiName);
                    String masterFunctionCode = masterSlaveFunction.getSlave2MasterFunctionCodes().get(checkFunctionCode);
                    if (Objects.nonNull(masterFunctionCode)) {
                        checkFunctionCode = masterFunctionCode;
                    }
                }

                Boolean hasFunction = checkResult.getResult().getOrDefault(checkFunctionCode, false);
                result.computeIfAbsent(apiName, k -> Maps.newHashMap()).put(funcCheckParam.getActionCode(), hasFunction);
            }
        }

        stopWatch.logSlow(5000);
    }

    private void checkFuncPrivilegeByProxy(Map<String, Map<String, Boolean>> result, User user,
                                           List<String> objectApiNames, List<String> actionCodes) {
        StopWatch stopWatch = StopWatch.create("checkFuncPrivilegeByProxy");
        List<FuncCheckParam> funcCheckParams = Lists.newArrayList();
        objectApiNames.forEach(objectApiName ->
                funcCheckParams.addAll(buildFuncCheckParams(result, objectApiName, actionCodes)));

        List<String> functionCodes = funcCheckParams.stream().map(FuncCheckParam::getFuncCode).collect(Collectors.toList());
        CheckFunctionPrivilege.Result checkResult = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(buildAuthContext(user), functionCodes);
        stopWatch.lap("checkFunctionPrivilege");
        if (!checkResult.isSuccess() || checkResult.getResult() == null) {
            log.warn("funPrivilegeCheck failed,tenantId:{},userId:{},functionCodes:{},result:{}", user.getTenantId(),
                    user.getUserId(), functionCodes, checkResult);
            return;
        }

        funcCheckParams.forEach(x -> {
            result.get(x.getApiName()).put(x.getActionCode(),
                    checkResult.getResult().getOrDefault(x.getFuncCode(), false));
        });
        stopWatch.logSlow(5000);
    }

    private boolean isApiNameInWhiteListSlaveUseMaster(String apiName) {
        return PrivilegeConstants.FUNC_CHECK_WHITE_LIST_API_NAMES_SlaveUseMaster.contains(apiName);
    }

    private boolean isApiNameInWhiteList(String apiName) {
        return PrivilegeConstants.FUNC_CHECK_WHITE_LIST_API_NAMES.contains(apiName);
    }

    public Set<String> getWhiteApiNameFromConfig() {
        return AppFrameworkConfig.getFuncCheckWhiteApiNameList();
    }

    private boolean isActionCodeInWhiteList(String actionCode) {
        return PrivilegeConstants.FUNC_CHECK_WHITE_LIST_ACTION_CODES.contains(actionCode);
    }

    @Deprecated
    private AuthContext buildOldAuthContext(User user, String appId) {
        return AuthContext.builder()
                .appId(AuthContextExt.getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.isOutUser() ? user.getOutUserId() : user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .outerUserId(user.getOutUserId())
                .properties(AuthContextExt.buildProperties(user))
                .identityType(RequestUtil.getOutIdentityType())
                .build();
    }

    @Deprecated
    private AuthContext buildOldAuthContext(User user) {
        return AuthContext.builder()
                .appId(AuthContextExt.getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.isOutUser() ? user.getOutUserId() : user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .properties(AuthContextExt.buildProperties(user))
                .identityType(RequestUtil.getOutIdentityType())
                .build();
    }

    private com.facishare.paas.auth.model.AuthContext buildAuthContext(User user, String appId) {
        return com.facishare.paas.auth.model.AuthContext.builder()
                .appId(AuthContextExt.getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.isOutUser() ? user.getOutUserId() : user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .outerUserId(user.getOutUserId())
                .identityType(RequestUtil.getOutIdentityType())
                .properties(AuthContextExt.buildProperties(user))
                .build();
    }

    private com.facishare.paas.auth.model.AuthContext buildAuthContext(User user) {
        return com.facishare.paas.auth.model.AuthContext.builder()
                .appId(AuthContextExt.getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.isOutUser() ? user.getOutUserId() : user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .identityType(RequestUtil.getOutIdentityType())
                .properties(AuthContextExt.buildProperties(user))
                .build();
    }

    @Override
    public void checkAndCacheFunctionPrivilege(User user, Map<String, Set<String>> objectActionCodeMap) {
        if (user.isSupperAdmin() || MapUtils.isEmpty(objectActionCodeMap)) {
            return;
        }
        Map<String, Map<String, Boolean>> result = Maps.newHashMap();
        List<FuncCheckParam> funcCheckParams = Lists.newArrayList();
        objectActionCodeMap.forEach((objectApiName, actionCodes) -> {
            fillResultWithWhiteList(result, Lists.newArrayList(objectApiName), Lists.newArrayList(actionCodes));
            funcCheckParams.addAll(buildFuncCheckParams(result, objectApiName, Lists.newArrayList(actionCodes)));
        });
        if (CollectionUtils.isEmpty(funcCheckParams)) {
            return;
        }

        Map<String, Boolean> funcCodeResult = Maps.newHashMap();
        List<String> funcCodeList = funcCheckParams.stream().map(FuncCheckParam::getFuncCode).collect(Collectors.toList());
        Map<String, Boolean> rpcResult = funcClient.userFuncPermissionCheck(buildAuthContext(user), Sets.newHashSet(funcCodeList));
        funcCodeResult.putAll(rpcResult);
        //不在返回值里的code默认为没权限
        funcCodeList.stream().filter(x -> !rpcResult.containsKey(x)).forEach(x -> funcCodeResult.put(x, Boolean.FALSE));
        //在context中缓存一份
        ContextCacheUtil.cacheFunctionPrivilege(user.getUserId(), funcCodeResult);
    }

    @Override
    public void queryAndCacheFuncPrivilege(User user, String objectApiName, Map<String, Set<String>> objectActionCodeMap) {
        if (user.isSupperAdmin()) {
            return;
        }
        Set<String> functionCodes = Sets.newHashSet();
        if (MapUtils.isNotEmpty(objectActionCodeMap)) {
            objectActionCodeMap.forEach((k, v) -> v.forEach(actionCode -> functionCodes.add(FunctionCodeBuilder.build(k, actionCode))));
        }
        try {
            Map<String, Boolean> rpcResult = funcClient.queryFuncCodePrefixAndExact(buildAuthContext(user), objectApiName, functionCodes);
            Map<String, Boolean> functionCodeResult = Maps.newHashMap();
            functionCodeResult.putAll(rpcResult);
            //不在返回值里的code默认为没权限
            functionCodes.stream().filter(x -> !rpcResult.containsKey(x)).forEach(x -> functionCodeResult.put(x, Boolean.FALSE));
            //在context中缓存一份
            ContextCacheUtil.cacheFunctionPrivilege(user.getUserId(), functionCodeResult);
        } catch (AuthException e) {
            throw new PermissionError(I18N.text(I18NKey.GET_FUNCTION_PERMISSION_FAIL), e.getCode());
        }
    }

    @Override
    public void copyFuncPrivilege(User user, String targetObjectApiName, String objectApiName, boolean copyField) {
        CopyObjFuncAndFieldPrivilege.Arg createFunctionPrivilegeArg = CopyObjFuncAndFieldPrivilege.Arg.builder()
                .authContext(buildOldAuthContext(user))
                .oldEntityId(targetObjectApiName)
                .newEntityId(objectApiName)
                .copyField(copyField)
                .build();

        CopyObjFuncAndFieldPrivilege.Result copyObjFuncResult = functionPrivilegeProxy.copyObjFunc(createFunctionPrivilegeArg, buildHeaders(user.getTenantId()));
        if (!copyObjFuncResult.isSuccess()) {
            log.warn("copyFuncPrivilege error,arg:{},result:{}", createFunctionPrivilegeArg, copyObjFuncResult);
            throw new PermissionError(I18N.text(I18NKey.INIT_PERMISSION_FAIL_REASON, copyObjFuncResult.getErrMessage()), copyObjFuncResult.getErrCode());
        }
    }

    @Override
    public Map<String, Boolean> checkFuncPrivilege(User user, List<String> funcCodes) {
        Map<String, Boolean> rpcResult = funcClient.userFuncPermissionCheck(buildAuthContext(user), Sets.newHashSet(funcCodes));
        return rpcResult;
    }

    @Data
    @Builder
    private static class FuncCheckParam {
        String apiName;
        String actionCode;
        String funcCode;
    }
}
