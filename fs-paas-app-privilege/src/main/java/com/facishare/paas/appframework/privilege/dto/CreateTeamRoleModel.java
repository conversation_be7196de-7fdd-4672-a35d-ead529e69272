package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Set;

public interface CreateTeamRoleModel {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg extends BasePrivilegeArg {

        private String roleName;
        private Set<String> entityIds;
        private String description;

    }


    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {

    }
}
