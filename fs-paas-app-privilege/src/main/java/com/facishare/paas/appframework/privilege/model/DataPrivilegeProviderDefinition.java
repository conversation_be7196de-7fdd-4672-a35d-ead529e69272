package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import lombok.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface DataPrivilegeProviderDefinition {

    interface BatchCheckBusinessPrivilege {
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        class Arg {
            Map<String, Permissions> dataPrivilegeMap;
            List<Map> dataList;
            Set<String> actionCodes;
            String describeApiName;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        class Result {
            private Map<String, Map<String, Permissions>> result;
        }

        @Data
        @ToString(callSuper = true)
        @EqualsAndHashCode(callSuper = true)
        class RestResult extends BaseAPIResult {
            private Result data;
        }
    }

    interface CheckBusinessPrivilege {
        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        class Arg {
            Map<String, Permissions> dataPrivilegeMap;
            List<Map> dataList;
            String actionCode;
            String describeApiName;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        class Result {
            private Map<String, Permissions> result;
        }

        @Data
        @ToString(callSuper = true)
        @EqualsAndHashCode(callSuper = true)
        class RestResult extends BaseAPIResult {
            private Result data;
        }
    }

}
