package com.facishare.paas.appframework.privilege.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ObjectDataCommonPrivilegeInfo {

  //对象ID
  @JsonProperty("ObjectDescribeApiName")
  @SerializedName("ObjectDescribeApiName")
  private String objectDescribeApiName;
  //对象名称
  @JsonProperty("ObjectDescribeDisplayName")
  @SerializedName("ObjectDescribeDisplayName")
  private String objectDescribeDisplayName;
  //权限类型
  @JsonProperty("PermissionType")
  @SerializedName("PermissionType")
  private String permissionType;

  //对象类型 0：普通对象 1：大对象
  private int objectType;
}
