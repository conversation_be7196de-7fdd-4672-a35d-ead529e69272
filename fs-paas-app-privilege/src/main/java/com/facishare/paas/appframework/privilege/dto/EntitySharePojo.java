package com.facishare.paas.appframework.privilege.dto;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.google.common.base.Strings;

import java.util.Map;

/**
 * Created by yusb on 2017/11/1.
 */
@Data
@Builder
public class EntitySharePojo {
    private String id;
    private String tenantId;
    private String appId;
    private String entityId;
    private Integer shareType;
    private String shareId;
    private Integer receiveType;
    private String receiveId;
    private String receiveTenantId;
    private Integer permission;
    private Integer status;
    private Boolean delFlag;
    private String creator;
    private Long createTime;
    private Long modifyTime;
    private String modifier;
    private Integer receiveDeptCascade;
    private int basedType;

    @AllArgsConstructor(staticName = "of")
    public static class EntitySharePojoHelper {
        /**
         * type、id、name
         */
        private Table<Integer, String, String> rangeTable;
        private Map<String, IObjectDescribe> describeMap;

        private String getNameByTypeAndId(Integer type, String id) {
            String name = rangeTable.get(type, id);
            if (Strings.isNullOrEmpty(name)) {
                return id;
            }
            return name;
        }

        private String getDisplayName(String apiName) {
            if (CollectionUtils.empty(describeMap)) {
                return apiName;
            }

            if (describeMap.containsKey(apiName)) {
                return describeMap.get(apiName).getDisplayName();
            }
            return apiName;
        }

        public LogInfo.ObjectSnapshot toSnapshot(EntitySharePojo entitySharePojo) {
            String textMessage = getTextMessage(entitySharePojo);
            return LogInfo.ObjectSnapshot.builder()
                    .textMsg(Lists.newArrayList(new LogInfo.LintMessage(textMessage, null, entitySharePojo.getEntityId())))
                    .snapshot(JSON.parseObject(JSON.toJSONString(entitySharePojo)))
                    .build();
        }

        private String getTextMessage(EntitySharePojo entitySharePojo) {
            String shareId = getNameByTypeAndId(entitySharePojo.getShareType(), entitySharePojo.getShareId());
            String receiveId = getNameByTypeAndId(entitySharePojo.getReceiveType(), entitySharePojo.getReceiveId());
            return I18N.text(I18NKey.DATA_SHARING, getDisplayName(entitySharePojo.getEntityId()), shareId, receiveId, entitySharePojo.getPermission());
        }
    }
}
