package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

/**
 * Created by luxin on 2018/4/24.
 */
public interface UserFieldPermissModel {

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private Map<String, Integer> result;
        private Boolean success;
    }

    @Data
    @AllArgsConstructor
    class Arg {
        private AuthContext authContext;
        String entityId;
    }


}
