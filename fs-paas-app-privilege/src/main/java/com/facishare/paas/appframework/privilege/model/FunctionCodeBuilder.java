package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.google.common.base.Joiner;

public abstract class FunctionCodeBuilder {

    private static final Joiner FUNC_CODE_JOINER = Joiner.on(PrivilegeConstants.FUNC_CODE_SPLIT);

    public static String build(String apiName, String actionCode) {
        //特殊处理AccountAttObj
        if (ObjectAPINameMapping.AccountAtt.getApiName().equals(actionCode)) {
            return buildFunctionCodeForAccountAttObj(actionCode);
        }

        //自定义按钮直接使用默认规则拼接
        if (ObjectAction.isCustomAction(actionCode)) {
            return defaultFunctionCode(apiName, actionCode);
        }

        String privilegeCode = getPrivilegeCodeByActionCode(apiName, actionCode);

        //SFA对象没有converter或者没有映射以及自定义对象使用默认规则拼接
        return defaultFunctionCode(apiName, privilegeCode);
    }

    private static String getPrivilegeCodeByActionCode(String apiName, String actionCode) {
        ObjectAction objectAction = ObjectAction.of(actionCode);
        if (objectAction == ObjectAction.UNKNOWN_ACTION) {
            return actionCode;
        } else {
            return objectAction.getPrivilegeCode(ObjectAPINameMapping.isSFAObject(apiName));
        }
    }

    private static String defaultFunctionCode(String apiName, String privilegeCode) {
        if (ObjectAction.VIEW_LIST.getActionCode().equals(privilegeCode)) {
            return apiName;
        }
        if (ObjectAction.DUPLICATECHECK.getActionCode().equals(privilegeCode)
                || PrivilegeConstants.AI_OBJECT_USER_LICENSE.equals(privilegeCode)) {
            return privilegeCode;
        }
        return FUNC_CODE_JOINER.join(apiName, privilegeCode);
    }

    private static String buildFunctionCodeForAccountAttObj(String actionCode) {
        ObjectAction action = ObjectAction.of(actionCode);
        switch (action) {
            case VIEW_LIST:
            case VIEW_DETAIL:
                actionCode = PrivilegeConstants.AccountAttrObj.VIEW_ACTION;
                break;
            case CREATE:
            case DELETE:
                actionCode = PrivilegeConstants.AccountAttrObj.EDIT_ACTION;
                break;
            default:
                break;
        }
        return actionCode;
    }

}
