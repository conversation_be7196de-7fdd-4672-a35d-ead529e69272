package com.facishare.paas.appframework.privilege.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Delegate;

import java.util.List;

/**
 * Created by yusb on 2017/4/24.
 */
public class GetBaseDataPrivilegeRulesModel {

    //1. entityId ,scope, permission 参数具体说明请参考“ 数据结构设计-》对象级权限”
    // entityId	对象实体Id
    // scope	对象权限范围
    // permission	操作权限
    //2. entityOpenness, page 参数都可以为空，page如果为空取默认值(pageSize = 10000, currentPage = 1)

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends BasePrivilegeArg {
        private List<String> entitys;
        private Integer permission;
        private Integer scope;
        private BasePageInfoDataPrivilege page;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BasePrivilegeResult {
        @Delegate
        private BaseResultDataContent result;

        @Data
        public static class BaseResultDataContent {
            private List<EntityOpennessPojo> content;
            private BasePageInfoDataPrivilege page;
        }
    }
}
