package com.facishare.paas.appframework.privilege;


import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege.FunctionPojo;
import com.facishare.paas.appframework.privilege.dto.PrivilegeResult;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 封装权限接口
 * <p>
 * Created by liyiguang on 2017/6/21.
 */
public interface FunctionPrivilegeService {

    boolean funPrivilegeCheck(User user, String objectApiName, String actionCode);

    Map<String, Boolean> funPrivilegeCheck(User user, String objectApiName, List<String> actionCodes);

    List<String> funPrivilegeCheck(User user, List<String> objectApiNames, String actionCode);

    Map<String, Map<String, Boolean>> batchFunPrivilegeCheck(User user, List<String> objectApiName,
                                                             List<String> actionCodes);

    /**
     * 批量检查 当前用户的权限码的权限
     *
     * @param user
     * @param funcCodes paas数据库中的权限码,不需要额外转换
     * @return
     */
    Map<String, Boolean> batchFuncCodePrivilegeCheck(User user, List<String> funcCodes);

    /**
     * 批量查询员工目标对象多个actionCode的权限
     *
     * @param context
     * @param apiName2ActionCodes 对象和需要查询actionCodes对应关系
     * @return
     */
    PrivilegeResult batchObjectFuncPrivilegeCheck(RequestContext context, Map<String, List<String>> apiName2ActionCodes);

    Set<String> queryDeptAndOrgIdsByScope(User user);

    void doFunPrivilegeCheck(User user, String objectApiName, List<String> actionCodes);

    boolean funDataViewPrivilegeCheck(User user, IObjectDescribe describe);

    boolean funDataViewPrivilegeCheck(User user, String objectApiName);

    Map<String, Boolean> funDataPrivilegeCheck(User user, String objectApiName, List<String> actionCodes);


    /**
     * 获取当前用户对当前Describe的所有无读权限字段的集合
     *
     * @param user          用户信息
     * @param objectApiName describeApiName
     * @return 不可读的字段集合
     */
    Set<String> getUnauthorizedFields(User user, String objectApiName);

    Set<String> getUnauthorizedFieldsByRole(User user, String roleCode, String objectApiName);

    /**
     * 获取当前用户对当前Describe的所有无写权限字段的集合
     *
     * @param user          用户信息
     * @param objectApiName describeApiName
     * @return 不可写的字段集合
     */
    Set<String> getReadonlyFields(User user, String objectApiName);

    /**
     * 获取当前用户对指定Describe的所有无读权限字段的集合
     *
     * @param user           用户信息
     * @param objectApiNames describeApiNames
     * @return 不可读的字段集合
     */
    Map<String, Set<String>> getUnauthorizedFields(User user, List<String> objectApiNames);

    /**
     * 初始化企业的功能权限
     *
     * @param user
     * @param objectApiName
     */
    void initFunctionPrivilege(User user, String objectApiName);

    void initFunctionPrivilege(User user, IObjectDescribe describe);

    void deleteFunctionPrivilege(User user, String objectApiName);

    /**
     * 更新老对象的功能权限(由原数字更新为标准字符串)
     *
     * @param user
     * @param objectApiName
     */
    void updateSfaFunctionPrivilege(User user, String objectApiName, String detailApiName, Map<String, String> functionMapping);

    /**
     * 获取企业的角色列表,去除订货通角色
     *
     * @param user
     * @return
     */
    List<Map<String, String>> getRoleList(User user);


    /**
     * 获取企业的全部 roleCodes
     *
     * @param user
     * @return
     */
    List<String> getAllRoleCodes(User user);

    /**
     * 批量删除功能权限
     *
     * @param user    用户信息
     * @param funcSet 权限code列表
     */
    void batchDelFunc(User user, List<String> funcSet);

    List<FunctionPojo> batchCreateFunc(User user, String describeApiName, List<String> actionCodes);

    List<FunctionPojo> batchCreateFunc(User user, List<FunctionPojo> functions);

    /**
     * 自定义对象添加自定义权限码,funcType=2
     *
     * @param user
     * @param objectApiName
     * @param actionCode
     * @param actionDisplayName
     */
    void createFuncCode(User user, String objectApiName, String actionCode, String actionDisplayName);

    /**
     * 自定义对象批量添加自定义按钮权限码,funcType=2
     *
     * @param user
     * @param buttons
     */
    void batchCreateFuncCode(User user, List<IUdefButton> buttons);

    /**
     * 批量角色同一个对象增加多个功能权限
     *
     * @param user
     * @param objectApiName
     * @param actionCode
     * @param roles
     */
    void rolesAddFuncAccess(User user, String objectApiName, String actionCode, List<String> roles);

    /**
     * 同一个角色多个actionCode增加权限
     *
     * @param user
     * @param roleCode
     * @param objectApiName
     * @param addActionCodes    增加权限的actionCodes
     * @param deleteActionCodes 删除权限的actionCodes
     */
    void updateUserDefinedFuncAccess(User user, String roleCode, String objectApiName, List<String> addActionCodes, List<String> deleteActionCodes);


    void updateUserDefinedFuncAccess(User user, String roleCode, List<String> addFuncCodes, List<String> deleteFuncCodes);

    Map<String, List<String>> getHaveFuncCodesPrivilegeRoles(User user, List<String> funcCodes);

    /**
     * 更新roleCode的功能权限,appId 默认是CRM
     *
     * @param user
     * @param roleCode
     * @param needAddPrivilegeFuncCodes    funcCode like "Account||Edit"
     * @param needDeletePrivilegeFuncCodes funcCode like "Account||Edit"
     */
    void updatePreDefinedFuncAccess(User user, String roleCode, List<String> needAddPrivilegeFuncCodes, List<String> needDeletePrivilegeFuncCodes);


    /**
     * 更新roleCode的功能权限
     *
     * @param user
     * @param appId
     * @param roleCode
     * @param needAddPrivilegeFuncCodes    funcCode like "Account||Edit"
     * @param needDeletePrivilegeFuncCodes funcCode like "Account||Edit"
     */
    void updatePreDefinedFuncAccess(User user, String appId, String roleCode, List<String> needAddPrivilegeFuncCodes, List<String> needDeletePrivilegeFuncCodes);


    /**
     * 获得具有目标actionCode权限的角色列表
     *
     * @param user
     * @param objectApiName
     * @param actionCode
     * @return
     */
    List<String> getHavePrivilegeRolesByActionCode(User user, String objectApiName, String actionCode);

    /**
     * 删除自定义对象的某个actionCode
     *
     * @param user
     * @param objectApiName
     * @param actionCode
     * @return
     */
    void deleteUserDefinedActionCode(User user, String objectApiName, String actionCode);

    Map<String, List<String>> getHavePrivilegeRolesByActionCodes(User user, String describeApiName, List<String> functionCodes);

    void checkAndCacheFunctionPrivilege(User user, Map<String, Set<String>> objectActionCodeMap);

    void queryAndCacheFuncPrivilege(User user, String objectApiName, Map<String, Set<String>> objectActionCodeMap);

    void copyFuncPrivilege(User user, String targetObjectApiName, String objectApiName, boolean copyField);

    Map<String, Boolean> checkFuncPrivilege(User user, List<String> funcCodes);
}
