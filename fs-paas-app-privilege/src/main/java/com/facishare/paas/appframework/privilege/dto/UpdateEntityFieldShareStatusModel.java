package com.facishare.paas.appframework.privilege.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by linqy on 2018/01/19.
 */
public class UpdateEntityFieldShareStatusModel {
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends BasePrivilegeArg {
        private List<String> ruleCodes;
        private Integer status;

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Result extends BasePrivilegeResult {
    }
}
