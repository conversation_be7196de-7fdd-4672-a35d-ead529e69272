package com.facishare.paas.appframework.privilege.dto;

import org.apache.poi.ss.formula.functions.Today;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/7/12 14:03
 */
public interface ForbiddenUsers {
    @Data
    @AllArgsConstructor
    class Arg {
        private AuthContext authContext;
        private List<String> orgIds;
        private Boolean forbiddenFlag;
    }

    @Data
    @AllArgsConstructor
    class Result {
        private Integer errCode;
        private String errMessage;
        private Boolean success;
        // TODO: 2018/7/16  
        private Integer quota;
    }

}
