package com.facishare.paas.appframework.privilege;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.privilege.dto.AddRoleModel;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.RestResult;
import com.facishare.paas.appframework.privilege.dto.RoleInfoListByTypesModel;
import com.facishare.paas.appframework.privilege.model.role.Role;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RoleServiceImpl的JUnit5测试类
 * 从Groovy测试转换而来，并增加了更多测试方法以提升覆盖率
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class RoleServiceImplTest {

    @Mock
    private RoleProxy roleProxy;

    @InjectMocks
    private RoleServiceImpl roleService;

    private String tenantId;
    private String appId;

    @BeforeEach
    void setUp() {
        tenantId = "181818";
        appId = "CRM";
    }

    /**
     * 测试addPredefinedRole方法 - 正常场景
     */
    @Test
    @DisplayName("正常场景 - 测试addPredefinedRole方法")
    void testAddPredefinedRole_NormalCase() {
        try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
            // Mock I18N.text方法
            i18nMock.when(() -> I18N.text(anyString())).thenReturn("Mock I18N text");

            // 准备测试数据
            Role role = Role.CHANNEL_MANAGER;

            AddRoleModel.Result mockResult = mock(AddRoleModel.Result.class);
            when(mockResult.isSuccess()).thenReturn(true);

            // 配置Mock行为
            when(roleProxy.addRole(any(AddRoleModel.Arg.class), any(Map.class))).thenReturn(mockResult);

            // 执行被测试方法
            assertDoesNotThrow(() -> {
                roleService.addPredefinedRole(tenantId, role);
            });

            // 验证调用
            verify(roleProxy).addRole(any(AddRoleModel.Arg.class), any(Map.class));
        }
    }

    /**
     * 测试addPredefinedRole方法 - 失败场景
     */
    @Test
    @DisplayName("异常场景 - 测试addPredefinedRole方法失败")
    void testAddPredefinedRole_FailureCase() {
        try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
            // Mock I18N.text方法
            i18nMock.when(() -> I18N.text(anyString())).thenReturn("Mock I18N text");

            // 准备测试数据
            Role role = Role.CHANNEL_MANAGER;

            AddRoleModel.Result mockResult = mock(AddRoleModel.Result.class);
            when(mockResult.isSuccess()).thenReturn(false);
            when(mockResult.getErrCode()).thenReturn(500);
            when(mockResult.getErrMessage()).thenReturn("添加角色失败");

            // 配置Mock行为
            when(roleProxy.addRole(any(AddRoleModel.Arg.class), any(Map.class))).thenReturn(mockResult);

            // 执行被测试方法并验证异常
            assertThrows(PermissionError.class, () -> {
                roleService.addPredefinedRole(tenantId, role);
            });

            // 验证调用
            verify(roleProxy).addRole(any(AddRoleModel.Arg.class), any(Map.class));
        }
    }

    /**
     * 测试getOuterRoleInfoList方法 - 正常场景
     */
    @Test
    @DisplayName("正常场景 - 测试getOuterRoleInfoList方法")
    void testGetOuterRoleInfoList_NormalCase() {
        // 准备测试数据
        RestResult.RoleInfoPojo roleInfo1 = new RestResult.RoleInfoPojo();
        roleInfo1.setRoleCode("role1");
        roleInfo1.setRoleName("角色1");
        
        RestResult.RoleInfoPojo roleInfo2 = new RestResult.RoleInfoPojo();
        roleInfo2.setRoleCode("role2");
        roleInfo2.setRoleName("角色2");
        
        List<RestResult.RoleInfoPojo> expectedRoles = Lists.newArrayList(roleInfo1, roleInfo2);
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(expectedRoles);
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        List<RestResult.RoleInfoPojo> result = roleService.getOuterRoleInfoList(tenantId, appId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("role1", result.get(0).getRoleCode());
        assertEquals("role2", result.get(1).getRoleCode());
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试getOuterRoleInfoList方法 - 失败场景
     */
    @Test
    @DisplayName("异常场景 - 测试getOuterRoleInfoList方法失败")
    void testGetOuterRoleInfoList_FailureCase() {
        // 准备测试数据
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        when(mockResult.isSuccess()).thenReturn(false);
        when(mockResult.getErrMessage()).thenReturn("获取角色列表失败");
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法并验证异常
        assertThrows(PermissionError.class, () -> {
            roleService.getOuterRoleInfoList(tenantId, appId);
        });
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试getCrmRoleInfoList方法 - 正常场景
     */
    @Test
    @DisplayName("正常场景 - 测试getCrmRoleInfoList方法")
    void testGetCrmRoleInfoList_NormalCase() {
        // 准备测试数据
        RestResult.RoleInfoPojo roleInfo = new RestResult.RoleInfoPojo();
        roleInfo.setRoleCode("crmRole1");
        roleInfo.setRoleName("CRM角色1");
        
        List<RestResult.RoleInfoPojo> expectedRoles = Lists.newArrayList(roleInfo);
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(expectedRoles);
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        List<RestResult.RoleInfoPojo> result = roleService.getCrmRoleInfoList(tenantId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("crmRole1", result.get(0).getRoleCode());
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试getCrmRoleInfoList方法 - 失败场景
     */
    @Test
    @DisplayName("异常场景 - 测试getCrmRoleInfoList方法失败")
    void testGetCrmRoleInfoList_FailureCase() {
        // 准备测试数据
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        when(mockResult.isSuccess()).thenReturn(false);
        when(mockResult.getErrMessage()).thenReturn("获取CRM角色列表失败");
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法并验证异常
        assertThrows(PermissionError.class, () -> {
            roleService.getCrmRoleInfoList(tenantId);
        });
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试checkRoleIsExist方法 - 角色存在场景
     */
    @Test
    @DisplayName("正常场景 - 测试checkRoleIsExist方法角色存在")
    void testCheckRoleIsExist_RoleExists() {
        // 准备测试数据
        String roleCode = "testRole";
        
        RestResult.RoleInfoPojo roleInfo1 = new RestResult.RoleInfoPojo();
        roleInfo1.setRoleCode("otherRole");
        roleInfo1.setRoleName("其他角色");
        
        RestResult.RoleInfoPojo roleInfo2 = new RestResult.RoleInfoPojo();
        roleInfo2.setRoleCode(roleCode);
        roleInfo2.setRoleName("测试角色");
        
        List<RestResult.RoleInfoPojo> roles = Lists.newArrayList(roleInfo1, roleInfo2);
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(roles);
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        Boolean result = roleService.checkRoleIsExist(tenantId, appId, roleCode);
        
        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试checkRoleIsExist方法 - 角色不存在场景
     */
    @Test
    @DisplayName("正常场景 - 测试checkRoleIsExist方法角色不存在")
    void testCheckRoleIsExist_RoleNotExists() {
        // 准备测试数据
        String roleCode = "nonExistentRole";
        
        RestResult.RoleInfoPojo roleInfo = new RestResult.RoleInfoPojo();
        roleInfo.setRoleCode("otherRole");
        roleInfo.setRoleName("其他角色");
        
        List<RestResult.RoleInfoPojo> roles = Lists.newArrayList(roleInfo);
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(roles);
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        Boolean result = roleService.checkRoleIsExist(tenantId, appId, roleCode);
        
        // 验证结果
        assertFalse(result);
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试checkRoleIsExist方法 - 失败场景
     */
    @Test
    @DisplayName("异常场景 - 测试checkRoleIsExist方法失败")
    void testCheckRoleIsExist_FailureCase() {
        // 准备测试数据
        String roleCode = "testRole";
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        when(mockResult.isSuccess()).thenReturn(false);
        when(mockResult.getErrMessage()).thenReturn("检查角色是否存在失败");
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法并验证异常
        assertThrows(PermissionError.class, () -> {
            roleService.checkRoleIsExist(tenantId, appId, roleCode);
        });
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试addPredefinedRole方法 - 不同角色类型
     */
    @Test
    @DisplayName("正常场景 - 测试addPredefinedRole方法不同角色类型")
    void testAddPredefinedRole_DifferentRoleTypes() {
        try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
            // Mock I18N.text方法
            i18nMock.when(() -> I18N.text(anyString())).thenReturn("Mock I18N text");

            // 准备测试数据
            Role role = Role.AGENT;

            AddRoleModel.Result mockResult = mock(AddRoleModel.Result.class);
            when(mockResult.isSuccess()).thenReturn(true);

            // 配置Mock行为
            when(roleProxy.addRole(any(AddRoleModel.Arg.class), any(Map.class))).thenReturn(mockResult);

            // 执行被测试方法
            assertDoesNotThrow(() -> {
                roleService.addPredefinedRole(tenantId, role);
            });

            // 验证调用参数
            verify(roleProxy).addRole(argThat(arg -> {
                return role.getRoleCode().equals(arg.getRoleCode())
                    && role.getDisplayName().equals(arg.getRoleName())
                    && role.getDescription().equals(arg.getDescription())
                    && Integer.valueOf(role.getType().getType()).equals(arg.getRoleType());
            }), any(Map.class));
        }
    }

    /**
     * 测试addPredefinedRole方法 - 空角色参数
     */
    @Test
    @DisplayName("边界场景 - 测试addPredefinedRole方法空角色")
    void testAddPredefinedRole_NullRole() {
        // 执行被测试方法并验证异常
        assertThrows(NullPointerException.class, () -> {
            roleService.addPredefinedRole(tenantId, null);
        });
        
        // 验证没有调用proxy
        verifyNoInteractions(roleProxy);
    }

    /**
     * 测试addPredefinedRole方法 - 空租户ID
     */
    @Test
    @DisplayName("边界场景 - 测试addPredefinedRole方法空租户ID")
    void testAddPredefinedRole_NullTenantId() {
        try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
            // Mock I18N.text方法
            i18nMock.when(() -> I18N.text(anyString())).thenReturn("Mock I18N text");

            // 准备测试数据
            Role role = Role.CHANNEL_MANAGER;

            AddRoleModel.Result mockResult = mock(AddRoleModel.Result.class);
            when(mockResult.isSuccess()).thenReturn(true);

            // 配置Mock行为
            when(roleProxy.addRole(any(AddRoleModel.Arg.class), any(Map.class))).thenReturn(mockResult);

            // 执行被测试方法
            assertDoesNotThrow(() -> {
                roleService.addPredefinedRole(null, role);
            });

            // 验证调用
            verify(roleProxy).addRole(any(AddRoleModel.Arg.class), any(Map.class));
        }
    }

    /**
     * 测试getOuterRoleInfoList方法 - 空应用ID
     */
    @Test
    @DisplayName("边界场景 - 测试getOuterRoleInfoList方法空应用ID")
    void testGetOuterRoleInfoList_NullAppId() {
        // 准备测试数据
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(Lists.newArrayList());
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        List<RestResult.RoleInfoPojo> result = roleService.getOuterRoleInfoList(tenantId, null);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试getOuterRoleInfoList方法 - 返回null结果
     */
    @Test
    @DisplayName("边界场景 - 测试getOuterRoleInfoList方法返回null结果")
    void testGetOuterRoleInfoList_NullResult() {
        // 准备测试数据
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);

        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(null);

        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);

        // 执行被测试方法 - 当getRoles()返回null时，方法会直接返回null
        List<RestResult.RoleInfoPojo> result = roleService.getOuterRoleInfoList(tenantId, appId);

        // 验证结果 - 当roles为null时，方法返回null（这是实际的业务逻辑）
        assertNull(result);

        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试getCrmRoleInfoList方法 - 空租户ID
     */
    @Test
    @DisplayName("边界场景 - 测试getCrmRoleInfoList方法空租户ID")
    void testGetCrmRoleInfoList_NullTenantId() {
        // 准备测试数据
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(Lists.newArrayList());
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        List<RestResult.RoleInfoPojo> result = roleService.getCrmRoleInfoList(null);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试checkRoleIsExist方法 - 空角色代码
     */
    @Test
    @DisplayName("边界场景 - 测试checkRoleIsExist方法空角色代码")
    void testCheckRoleIsExist_NullRoleCode() {
        // 准备测试数据
        RestResult.RoleInfoPojo roleInfo = new RestResult.RoleInfoPojo();
        roleInfo.setRoleCode("testRole");
        roleInfo.setRoleName("测试角色");
        
        List<RestResult.RoleInfoPojo> roles = Lists.newArrayList(roleInfo);
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(roles);
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法并验证异常
        assertThrows(NullPointerException.class, () -> {
            roleService.checkRoleIsExist(tenantId, appId, null);
        });
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试checkRoleIsExist方法 - 空角色列表
     */
    @Test
    @DisplayName("边界场景 - 测试checkRoleIsExist方法空角色列表")
    void testCheckRoleIsExist_EmptyRoleList() {
        // 准备测试数据
        String roleCode = "testRole";
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(Lists.newArrayList());
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        Boolean result = roleService.checkRoleIsExist(tenantId, appId, roleCode);
        
        // 验证结果
        assertFalse(result);
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试checkRoleIsExist方法 - 角色代码匹配但大小写不同
     */
    @Test
    @DisplayName("边界场景 - 测试checkRoleIsExist方法大小写敏感")
    void testCheckRoleIsExist_CaseSensitive() {
        // 准备测试数据
        String roleCode = "TestRole";
        
        RestResult.RoleInfoPojo roleInfo = new RestResult.RoleInfoPojo();
        roleInfo.setRoleCode("testRole"); // 小写
        roleInfo.setRoleName("测试角色");
        
        List<RestResult.RoleInfoPojo> roles = Lists.newArrayList(roleInfo);
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(roles);
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        Boolean result = roleService.checkRoleIsExist(tenantId, appId, roleCode);
        
        // 验证结果 - 应该返回false，因为大小写不匹配
        assertFalse(result);
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试checkRoleIsExist方法 - 多个角色中找到匹配的
     */
    @Test
    @DisplayName("正常场景 - 测试checkRoleIsExist方法多角色匹配")
    void testCheckRoleIsExist_MultipleRolesMatch() {
        // 准备测试数据
        String roleCode = "targetRole";
        
        RestResult.RoleInfoPojo roleInfo1 = new RestResult.RoleInfoPojo();
        roleInfo1.setRoleCode("role1");
        roleInfo1.setRoleName("角色1");
        
        RestResult.RoleInfoPojo roleInfo2 = new RestResult.RoleInfoPojo();
        roleInfo2.setRoleCode("role2");
        roleInfo2.setRoleName("角色2");
        
        RestResult.RoleInfoPojo roleInfo3 = new RestResult.RoleInfoPojo();
        roleInfo3.setRoleCode(roleCode);
        roleInfo3.setRoleName("目标角色");
        
        RestResult.RoleInfoPojo roleInfo4 = new RestResult.RoleInfoPojo();
        roleInfo4.setRoleCode("role4");
        roleInfo4.setRoleName("角色4");
        
        List<RestResult.RoleInfoPojo> roles = Lists.newArrayList(roleInfo1, roleInfo2, roleInfo3, roleInfo4);
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(roles);
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        Boolean result = roleService.checkRoleIsExist(tenantId, appId, roleCode);
        
        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(roleProxy).roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class));
    }

    /**
     * 测试所有方法的角色类型参数验证
     */
    @Test
    @DisplayName("参数验证 - 测试checkRoleIsExist方法角色类型参数")
    void testCheckRoleIsExist_RoleTypesParameter() {
        // 准备测试数据
        String roleCode = "testRole";
        
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(Lists.newArrayList());
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        roleService.checkRoleIsExist(tenantId, appId, roleCode);
        
        // 验证角色类型参数包含所有四种类型
        verify(roleProxy).roleInfoListByRoleTypes(argThat(arg -> {
            List<Integer> roleTypes = arg.getRoleTypes();
            return roleTypes != null 
                && roleTypes.size() == 4
                && roleTypes.contains(Role.RoleType.DEFAULT_ROLE.getType())
                && roleTypes.contains(Role.RoleType.USER_DEFINED_ROLE.getType())
                && roleTypes.contains(Role.RoleType.DEFAULT_OUTER_ROLE.getType())
                && roleTypes.contains(Role.RoleType.USER_DEFINED_OUTER_ROLE.getType());
        }), any(Map.class));
    }

    /**
     * 测试getOuterRoleInfoList方法的角色类型参数验证
     */
    @Test
    @DisplayName("参数验证 - 测试getOuterRoleInfoList方法角色类型参数")
    void testGetOuterRoleInfoList_RoleTypesParameter() {
        // 准备测试数据
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(Lists.newArrayList());
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        roleService.getOuterRoleInfoList(tenantId, appId);
        
        // 验证角色类型参数只包含外部角色类型
        verify(roleProxy).roleInfoListByRoleTypes(argThat(arg -> {
            List<Integer> roleTypes = arg.getRoleTypes();
            return roleTypes != null 
                && roleTypes.size() == 2
                && roleTypes.contains(Role.RoleType.DEFAULT_OUTER_ROLE.getType())
                && roleTypes.contains(Role.RoleType.USER_DEFINED_OUTER_ROLE.getType());
        }), any(Map.class));
    }

    /**
     * 测试getCrmRoleInfoList方法的角色类型参数验证
     */
    @Test
    @DisplayName("参数验证 - 测试getCrmRoleInfoList方法角色类型参数")
    void testGetCrmRoleInfoList_RoleTypesParameter() {
        // 准备测试数据
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(Lists.newArrayList());
        
        // 配置Mock行为
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        roleService.getCrmRoleInfoList(tenantId);
        
        // 验证角色类型参数只包含CRM角色类型
        verify(roleProxy).roleInfoListByRoleTypes(argThat(arg -> {
            List<Integer> roleTypes = arg.getRoleTypes();
            return roleTypes != null 
                && roleTypes.size() == 2
                && roleTypes.contains(Role.RoleType.DEFAULT_ROLE.getType())
                && roleTypes.contains(Role.RoleType.USER_DEFINED_ROLE.getType());
        }), any(Map.class));
    }

    /**
     * 测试buildAuthContext私有方法的逻辑（通过公共方法间接测试）
     */
    @Test
    @DisplayName("间接测试 - buildAuthContext方法逻辑")
    void testBuildAuthContext_IndirectTest() {
        // 准备测试数据
        RoleInfoListByTypesModel.Result mockResult = mock(RoleInfoListByTypesModel.Result.class);
        RestResult mockRestResult = mock(RestResult.class);
        
        when(mockResult.isSuccess()).thenReturn(true);
        when(mockResult.getResult()).thenReturn(mockRestResult);
        when(mockRestResult.getRoles()).thenReturn(Lists.newArrayList());
        
        // 配置Mock行为，验证AuthContext的构建
        when(roleProxy.roleInfoListByRoleTypes(any(RoleInfoListByTypesModel.Arg.class), any(Map.class)))
            .thenReturn(mockResult);
        
        // 执行被测试方法
        List<RestResult.RoleInfoPojo> result = roleService.getCrmRoleInfoList(tenantId);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证AuthContext的构建逻辑
        verify(roleProxy).roleInfoListByRoleTypes(argThat(arg -> {
            AuthContext authContext = arg.getAuthContext();
            return authContext != null 
                && "CRM".equals(authContext.getAppId())
                && tenantId.equals(authContext.getTenantId())
                && "-10000".equals(authContext.getUserId());
        }), any(Map.class));
    }
} 