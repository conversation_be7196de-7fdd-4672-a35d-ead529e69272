package com.facishare.paas.appframework.privilege.util;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;

/**
 * GenerateByAI
 * 测试内容描述：测试AuthContextExt工具类的方法
 */
@ExtendWith(MockitoExtension.class)
class AuthContextExtTest {

    // 移除静态初始化，改为在每个测试方法中使用MockedStatic

    // 移除setUp方法，避免I18N初始化问题

    /**
     * GenerateByAI
     * 测试内容描述：测试of方法，使用User参数的版本
     */
    @ParameterizedTest
    @MethodSource("provideOfWithUserTestData")
    void testOfWithUser(String tenantId, String userId, String outUserId, String outTenantId, 
                       String appId, String tenantIdConf, String outIdentityType) {
        // 测试准备
        User user = new User(tenantId, userId, outUserId, outTenantId);
        
        // 模拟 RequestUtil.getOutIdentityType()
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .outIdentityType(outIdentityType)
                .appId(appId)
                .build();
        RequestContextManager.setContext(requestContext);
        
        Set<String> graySet = "ALL".equals(tenantIdConf) ? Sets.newHashSet("ALL") : Sets.newHashSet(tenantIdConf);
        ReflectionTestUtils.setField(AppFrameworkConfig.class, "functionPermissionAppIdGrayEi", graySet);
        
        try (MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class);
             MockedStatic<AppIdUtil> appIdUtilMock = mockStatic(AppIdUtil.class)) {
            
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn(outIdentityType);
            requestUtilMock.when(RequestUtil::getAppId).thenReturn(appId);
            appIdUtilMock.when(() -> AppIdUtil.getAppId(user)).thenReturn(appId);
            
            // 执行被测试方法
            AuthContextExt result = AuthContextExt.of(user);
            
            // 结果验证
            assertNotNull(result);
            assertNotNull(result.getAuthContext());
            assertEquals(tenantId, result.getAuthContext().getTenantId());
            assertEquals(outUserId != null ? outUserId : userId, result.getAuthContext().getUserId());
            assertEquals(outIdentityType, result.getAuthContext().getIdentityType());
        }
    }

    static Stream<Arguments> provideOfWithUserTestData() {
        return Stream.of(
            Arguments.of("123", "1000", null, null, "custom-app", "74255", null),
            Arguments.of("123", "1000", "out-1", "out-123", "custom-app", "ALL", "app")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of方法，使用User和allAppScope参数的版本
     */
    @ParameterizedTest
    @MethodSource("provideOfWithUserAndAllAppScopeTestData")
    void testOfWithUserAndAllAppScope(String tenantId, String userId, String outUserId, String outTenantId, 
                                     String appId, boolean allAppScope, String tenantIdConf, String outIdentityType) {
        // 测试准备
        User user = new User(tenantId, userId, outUserId, outTenantId);
        
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .outIdentityType(outIdentityType)
                .appId(appId)
                .build();
        RequestContextManager.setContext(requestContext);
        
        Set<String> graySet = "ALL".equals(tenantIdConf) ? Sets.newHashSet("ALL") : Sets.newHashSet(tenantIdConf);
        ReflectionTestUtils.setField(AppFrameworkConfig.class, "functionPermissionAppIdGrayEi", graySet);
        
        try (MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class);
             MockedStatic<AppIdUtil> appIdUtilMock = mockStatic(AppIdUtil.class)) {
            
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn(outIdentityType);
            requestUtilMock.when(RequestUtil::getAppId).thenReturn(appId);
            appIdUtilMock.when(() -> AppIdUtil.getAppId(user)).thenReturn(appId);
            
            // 执行被测试方法
            AuthContextExt result = AuthContextExt.of(user, allAppScope);
            
            // 结果验证
            assertNotNull(result);
            assertNotNull(result.getAuthContext());
            assertEquals(tenantId, result.getAuthContext().getTenantId());
            assertEquals(outUserId != null ? outUserId : userId, result.getAuthContext().getUserId());
            assertEquals(!allAppScope, result.getAuthContext().getFilterAppId());
            assertEquals(outIdentityType, result.getAuthContext().getIdentityType());
        }
    }

    static Stream<Arguments> provideOfWithUserAndAllAppScopeTestData() {
        return Stream.of(
            Arguments.of("123", "1000", null, null, "custom-app", false, "74255", null),
            Arguments.of("123", "1000", null, null, "custom-app", true, "74255", null),
            Arguments.of("123", "1000", "out-1", "out-123", "custom-app", false, "ALL", "app"),
            Arguments.of("123", "1000", "out-1", "out-123", "custom-app", true, "ALL", "app")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试of方法，使用User、allAppScope和appId参数的版本
     */
    @ParameterizedTest
    @MethodSource("provideOfWithUserAndAllAppScopeAndAppIdTestData")
    void testOfWithUserAndAllAppScopeAndAppId(String tenantId, String userId, String outUserId, String outTenantId, 
                                             String userAppId, String inputAppId, boolean allAppScope, 
                                             String tenantIdConf, String outIdentityType) {
        // 测试准备
        User user = new User(tenantId, userId, outUserId, outTenantId);
        
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .outIdentityType(outIdentityType)
                .appId(userAppId)
                .build();
        RequestContextManager.setContext(requestContext);
        
        Set<String> graySet = "ALL".equals(tenantIdConf) ? Sets.newHashSet("ALL") : Sets.newHashSet(tenantIdConf);
        ReflectionTestUtils.setField(AppFrameworkConfig.class, "functionPermissionAppIdGrayEi", graySet);
        
        try (MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class);
             MockedStatic<AppIdUtil> appIdUtilMock = mockStatic(AppIdUtil.class)) {
            
            requestUtilMock.when(RequestUtil::getOutIdentityType).thenReturn(outIdentityType);
            requestUtilMock.when(RequestUtil::getAppId).thenReturn(userAppId);
            appIdUtilMock.when(() -> AppIdUtil.getAppId(user)).thenReturn(userAppId);
            
            // 执行被测试方法
            AuthContextExt result = AuthContextExt.of(user, allAppScope, inputAppId);
            
            // 结果验证
            assertNotNull(result);
            assertNotNull(result.getAuthContext());
            assertEquals(tenantId, result.getAuthContext().getTenantId());
            assertEquals(outUserId != null ? outUserId : userId, result.getAuthContext().getUserId());
            assertEquals(!allAppScope, result.getAuthContext().getFilterAppId());
            assertEquals(outIdentityType, result.getAuthContext().getIdentityType());
        }
    }

    static Stream<Arguments> provideOfWithUserAndAllAppScopeAndAppIdTestData() {
        return Stream.of(
            Arguments.of("123", "1000", null, null, "custom-app", "custom-app2", false, "74255", null),
            Arguments.of("123", "1000", null, null, "custom-app", "custom-app2", true, "74255", null),
            Arguments.of("123", "1000", "out-1", "out-123", "custom-app", "custom-app2", false, "ALL", "app"),
            Arguments.of("123", "1000", "out-1", "out-123", "custom-app", "custom-app2", true, "ALL", "app")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildProperties方法，使用User参数的版本
     */
    @ParameterizedTest
    @MethodSource("provideBuildPropertiesWithUserTestData")
    void testBuildPropertiesWithUser(String tenantId, String userId, String outUserId, String outTenantId, 
                                    String appId, String tenantIdConf) {
        // 测试准备
        User user = new User(tenantId, userId, outUserId, outTenantId);
        
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .appId(appId)
                .build();
        RequestContextManager.setContext(requestContext);
        
        Set<String> graySet = "ALL".equals(tenantIdConf) ? Sets.newHashSet("ALL") : Sets.newHashSet(tenantIdConf);
        ReflectionTestUtils.setField(AppFrameworkConfig.class, "functionPermissionAppIdGrayEi", graySet);
        
        try (MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class)) {
            requestUtilMock.when(RequestUtil::getAppId).thenReturn(appId);
            
            // 执行被测试方法
            Map<String, String> result = AuthContextExt.buildProperties(user);
            
            // 结果验证
            if ("ALL".equals(tenantIdConf) && outUserId != null) {
                assertNotNull(result);
                assertEquals(appId, result.get("bizAppId"));
            } else {
                assertNull(result);
            }
        }
    }

    static Stream<Arguments> provideBuildPropertiesWithUserTestData() {
        return Stream.of(
            Arguments.of("123", "1000", null, null, "custom-app", "74255"),
            Arguments.of("123", "1000", null, null, "custom-app", "ALL"),
            Arguments.of("123", "1000", "out-1", "out-123", "custom-app", "74255"),
            Arguments.of("123", "1000", "out-1", "out-123", "custom-app", "ALL")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildProperties方法，使用tenantId、appId和isOutUser参数的版本
     */
    @ParameterizedTest
    @MethodSource("provideBuildPropertiesWithTenantIdAndAppIdAndIsOutUserTestData")
    void testBuildPropertiesWithTenantIdAndAppIdAndIsOutUser(String tenantId, String appId, boolean isOutUser, String tenantIdConf) {
        Set<String> graySet = "ALL".equals(tenantIdConf) ? Sets.newHashSet("ALL") : Sets.newHashSet(tenantIdConf);
        ReflectionTestUtils.setField(AppFrameworkConfig.class, "functionPermissionAppIdGrayEi", graySet);
        
        // 执行被测试方法
        Map<String, String> result = AuthContextExt.buildProperties(tenantId, appId, isOutUser);
        
        // 结果验证
        if ("ALL".equals(tenantIdConf) && isOutUser) {
            assertNotNull(result);
            assertEquals(appId, result.get("bizAppId"));
        } else {
            assertNull(result);
        }
    }

    static Stream<Arguments> provideBuildPropertiesWithTenantIdAndAppIdAndIsOutUserTestData() {
        return Stream.of(
            Arguments.of("123", "custom-app", false, "74255"),
            Arguments.of("123", "custom-app", false, "ALL"),
            Arguments.of("123", "custom-app", true, "74255"),
            Arguments.of("123", "custom-app", true, "ALL")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getAppId方法，使用User参数的版本
     */
    @ParameterizedTest
    @MethodSource("provideGetAppIdWithUserTestData")
    void testGetAppIdWithUser(String tenantId, String userId, String outUserId, String outTenantId, 
                             String tenantIdConf, String requestUtilAppId, String appIdUtilAppId) {
        // 测试准备
        User user = new User(tenantId, userId, outUserId, outTenantId);
        
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(user)
                .appId(requestUtilAppId)
                .build();
        RequestContextManager.setContext(requestContext);
        
        Set<String> graySet = "ALL".equals(tenantIdConf) ? Sets.newHashSet("ALL") : Sets.newHashSet(tenantIdConf);
        ReflectionTestUtils.setField(AppFrameworkConfig.class, "functionPermissionAppIdGrayEi", graySet);
        
        try (MockedStatic<RequestUtil> requestUtilMock = mockStatic(RequestUtil.class);
             MockedStatic<AppIdUtil> appIdUtilMock = mockStatic(AppIdUtil.class)) {
            
            requestUtilMock.when(RequestUtil::getAppId).thenReturn(requestUtilAppId);
            appIdUtilMock.when(() -> AppIdUtil.getAppId(user)).thenReturn(appIdUtilAppId);
            
            // 执行被测试方法
            String result = AuthContextExt.getAppId(user);
            
            // 结果验证
            if (outUserId != null) {
                if ("ALL".equals(tenantIdConf)) {
                    assertEquals("CRM", result);  // 使用 PrivilegeConstants.APP_ID 的值
                } else {
                    assertEquals(requestUtilAppId, result);
                }
            } else {
                assertEquals(appIdUtilAppId, result);
            }
        }
    }

    static Stream<Arguments> provideGetAppIdWithUserTestData() {
        return Stream.of(
            Arguments.of("123", "1000", null, null, "74255", "request-app", "request-app"),
            Arguments.of("123", "1000", "out-1", "out-123", "74255", "CRM", "request-app"),
            Arguments.of("123", "1000", "out-1", "out-123", "ALL", "request-app", "appIdUtil-app")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getAppId方法，使用tenantId、appId和isOutUser参数的版本
     */
    @ParameterizedTest
    @MethodSource("provideGetAppIdWithTenantIdAndAppIdAndIsOutUserTestData")
    void testGetAppIdWithTenantIdAndAppIdAndIsOutUser(String tenantId, String inputAppId, boolean isOutUser, 
                                                     String tenantIdConf, String appIdUtilResult) {
        Set<String> graySet = "ALL".equals(tenantIdConf) ? Sets.newHashSet("ALL") : Sets.newHashSet(tenantIdConf);
        ReflectionTestUtils.setField(AppFrameworkConfig.class, "functionPermissionAppIdGrayEi", graySet);
        
        try (MockedStatic<AppIdUtil> appIdUtilMock = mockStatic(AppIdUtil.class)) {
            appIdUtilMock.when(() -> AppIdUtil.getAppId(null, inputAppId)).thenReturn(appIdUtilResult);
            
            // 执行被测试方法
            String result = AuthContextExt.getAppId(tenantId, inputAppId, isOutUser);
            
            // 结果验证
            if (isOutUser) {
                if ("ALL".equals(tenantIdConf)) {
                    assertEquals("CRM", result);  // 使用 PrivilegeConstants.APP_ID 的值
                } else {
                    assertEquals(inputAppId, result);
                }
            } else {
                assertEquals(appIdUtilResult, result);
            }
        }
    }

    static Stream<Arguments> provideGetAppIdWithTenantIdAndAppIdAndIsOutUserTestData() {
        return Stream.of(
            Arguments.of("123", "custom-app", false, "74255", "custom-app"),
            Arguments.of("123", "custom-app", true, "74255", "appIdUtil-app"),
            Arguments.of("123", "custom-app", true, "ALL", "appIdUtil-app")
        );
    }
} 