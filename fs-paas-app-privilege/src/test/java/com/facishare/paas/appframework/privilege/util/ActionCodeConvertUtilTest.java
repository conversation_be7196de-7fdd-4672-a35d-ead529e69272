package com.facishare.paas.appframework.privilege.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * 测试内容描述：测试ActionCodeConvertUtil工具类的方法
 */
class ActionCodeConvertUtilTest {

    /**
     * GenerateByAI
     * 测试内容描述：测试convert2FuncCode方法，验证List动作码的特殊处理
     */
    @Test
    @DisplayName("测试convert2FuncCode方法 - List动作码特殊处理")
    void testConvert2FuncCode_ListActionCode() {
        // 准备测试数据
        String objectApiName = "TestObject";
        String actionCode = "List";
        
        // 执行被测试方法
        String result = ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode);
        
        // 验证结果
        assertEquals(objectApiName, result);
        assertEquals("TestObject", result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convert2FuncCode方法，验证非List动作码的处理
     */
    @ParameterizedTest
    @CsvSource({
            "TestObject, Create, TestObject||Create",
            "TestObject, Update, TestObject||Update",
            "TestObject, Delete, TestObject||Delete",
            "TestObject, View, TestObject||View",
            "TestObject, Export, TestObject||Export",
            "TestObject, Import, TestObject||Import",
            "CustomObject, CustomAction, CustomObject||CustomAction",
            "User, Login, User||Login",
            "Order, Approve, Order||Approve"
    })
    @DisplayName("参数化测试 - convert2FuncCode方法非List动作码")
    void testConvert2FuncCode_NonListActionCode(String objectApiName, String actionCode, String expected) {
        // 执行被测试方法
        String result = ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode);
        
        // 验证结果
        assertEquals(expected, result);
        assertTrue(result.contains("||"));
        assertTrue(result.startsWith(objectApiName));
        assertTrue(result.endsWith(actionCode));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convert2FuncCode方法，验证空值和边界情况
     */
    @ParameterizedTest
    @CsvSource(value = {
            "null, List, null",
            "'', List, ''",
            "TestObject, null, TestObject||null",
            "TestObject, '', TestObject||",
            "null, Create, null||Create",
            "'', Create, ||Create"
    }, nullValues = {"null"})
    @DisplayName("参数化测试 - convert2FuncCode方法边界情况")
    void testConvert2FuncCode_EdgeCases(String objectApiName, String actionCode, String expected) {
        // 执行被测试方法
        String result = ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode);
        
        // 验证结果
        assertEquals(expected, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convert2FuncCode方法，验证List动作码的大小写敏感性
     */
    @ParameterizedTest
    @CsvSource({
            "TestObject, list, TestObject||list",
            "TestObject, LIST, TestObject||LIST",
            "TestObject, List, TestObject",
            "TestObject, LiSt, TestObject||LiSt"
    })
    @DisplayName("参数化测试 - convert2FuncCode方法List大小写敏感")
    void testConvert2FuncCode_ListCaseSensitive(String objectApiName, String actionCode, String expected) {
        // 执行被测试方法
        String result = ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode);
        
        // 验证结果
        assertEquals(expected, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convert2FuncCode方法，验证特殊字符的处理
     */
    @ParameterizedTest
    @CsvSource({
            "Test-Object, Create, Test-Object||Create",
            "Test_Object, Update, Test_Object||Update",
            "Test.Object, Delete, Test.Object||Delete",
            "Test Object, View, Test Object||View",
            "TestObject, Create-Action, TestObject||Create-Action",
            "TestObject, Create_Action, TestObject||Create_Action",
            "TestObject, Create.Action, TestObject||Create.Action",
            "TestObject, Create Action, TestObject||Create Action"
    })
    @DisplayName("参数化测试 - convert2FuncCode方法特殊字符处理")
    void testConvert2FuncCode_SpecialCharacters(String objectApiName, String actionCode, String expected) {
        // 执行被测试方法
        String result = ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode);
        
        // 验证结果
        assertEquals(expected, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convert2FuncCode方法，验证分隔符"||"不会与输入冲突
     */
    @ParameterizedTest
    @CsvSource({
            "Test||Object, Create, Test||Object||Create",
            "TestObject, Create||Action, TestObject||Create||Action",
            "Test||Object, Create||Action, Test||Object||Create||Action"
    })
    @DisplayName("参数化测试 - convert2FuncCode方法分隔符冲突处理")
    void testConvert2FuncCode_SeparatorConflict(String objectApiName, String actionCode, String expected) {
        // 执行被测试方法
        String result = ActionCodeConvertUtil.convert2FuncCode(objectApiName, actionCode);
        
        // 验证结果
        assertEquals(expected, result);
        // 验证结果包含正确的分隔符数量
        long separatorCount = result.chars().filter(ch -> ch == '|').count();
        assertTrue(separatorCount >= 2); // 至少包含一个"||"分隔符
    }
} 