package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ContextCacheUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.privilege.dto.CheckFunctionPrivilege;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

import org.mockito.MockedStatic;

/**
 * GenerateByAI
 * 测试内容描述：测试MasterSlaveFunctionPrivilegeService类的方法
 */
@ExtendWith(MockitoExtension.class)
class MasterSlaveFunctionPrivilegeServiceTest {

    @Mock
    private IObjectDescribeService objectDescribeService;

    @Mock
    private FuncClient funcClient;

    @InjectMocks
    private MasterSlaveFunctionPrivilegeService masterSlaveFunctionPrivilegeService;

    private AuthContext testAuthContext;

    @BeforeEach
    void setUp() {
        testAuthContext = AuthContext.builder()
                .appId("test-app")
                .tenantId("123456")
                .userId("78910")
                .build();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionPrivilege方法正常场景
     */
    @Test
    @DisplayName("正常场景 - checkFunctionPrivilege方法成功获取权限")
    void testCheckFunctionPrivilege() {
        // 准备测试数据
        List<String> funcCodeList = Arrays.asList("Object1||Add", "Object1||Edit", "Object2||Delete");
        
        Map<String, Boolean> expectedResult = new HashMap<>();
        expectedResult.put("Object1||Add", true);
        expectedResult.put("Object1||Edit", true);
        expectedResult.put("Object2||Delete", false);

        // 配置Mock行为
        when(funcClient.userFuncPermissionCheck(any(AuthContext.class), any(Set.class)))
                .thenReturn(expectedResult);

        // 执行被测试方法
        CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(testAuthContext, funcCodeList);

        // 验证Mock交互
        verify(funcClient, times(1)).userFuncPermissionCheck(
                argThat(ctx -> ctx.getTenantId().equals(testAuthContext.getTenantId()) &&
                              ctx.getUserId().equals(testAuthContext.getUserId()) &&
                              ctx.getAppId().equals(testAuthContext.getAppId())),
                argThat(codes -> codes.size() == 3 &&
                               codes.containsAll(funcCodeList))
        );

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(expectedResult, result.getResult());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionPrivilege方法异常场景，接口调用失败
     */
    @Test
    @DisplayName("异常场景 - checkFunctionPrivilege方法接口调用失败")
    void testCheckFunctionPrivilegeError() {
        // 准备测试数据
        List<String> funcCodeList = Arrays.asList("Object1||Add", "Object1||Edit");
        AuthException authException = new AuthException(401, "权限验证失败");

        // 配置Mock行为
        when(funcClient.userFuncPermissionCheck(any(AuthContext.class), any(Set.class)))
                .thenThrow(authException);

        // 执行被测试方法
        CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(testAuthContext, funcCodeList);

        // 验证Mock交互
        verify(funcClient, times(1)).userFuncPermissionCheck(any(AuthContext.class), any(Set.class));

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals(authException.getCode(), result.getErrCode());
        assertEquals(authException.getMessage(), result.getErrMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionDataPrivilege方法正常场景
     */
    @Test
    @DisplayName("正常场景 - checkFunctionDataPrivilege方法成功获取权限")
    void testCheckFunctionDataPrivilege() {
        // 准备测试数据
        List<String> funcCodeList = Arrays.asList("Object1||Add", "Object1||Edit");
        
        Map<String, Boolean> expectedResult = new HashMap<>();
        expectedResult.put("Object1||Add", true);
        expectedResult.put("Object1||Edit", false);

        // 配置Mock行为
        when(funcClient.userFuncPermissionCheckWithoutAppId(any(AuthContext.class), any(Set.class)))
                .thenReturn(expectedResult);

        // 执行被测试方法
        CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionDataPrivilege(testAuthContext, funcCodeList);

        // 验证Mock交互
        verify(funcClient, times(1)).userFuncPermissionCheckWithoutAppId(
                argThat(ctx -> ctx.getTenantId().equals(testAuthContext.getTenantId()) &&
                              ctx.getUserId().equals(testAuthContext.getUserId()) &&
                              ctx.getAppId().equals(testAuthContext.getAppId())),
                argThat(codes -> codes.size() == funcCodeList.size() &&
                               codes.containsAll(funcCodeList))
        );

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(expectedResult, result.getResult());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionDataPrivilege方法异常场景
     */
    @Test
    @DisplayName("异常场景 - checkFunctionDataPrivilege方法接口调用失败")
    void testCheckFunctionDataPrivilegeError() {
        // 准备测试数据
        List<String> funcCodeList = Arrays.asList("Object1||Add", "Object1||Edit");
        AuthException authException = new AuthException(401, "权限验证失败");

        // 配置Mock行为
        when(funcClient.userFuncPermissionCheckWithoutAppId(any(AuthContext.class), any(Set.class)))
                .thenThrow(authException);

        // 执行被测试方法
        CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionDataPrivilege(testAuthContext, funcCodeList);

        // 验证Mock交互
        verify(funcClient, times(1)).userFuncPermissionCheckWithoutAppId(any(AuthContext.class), any(Set.class));

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals(authException.getCode(), result.getErrCode());
        assertEquals(authException.getMessage(), result.getErrMessage());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSlaveHasNoFunctionCodes方法基本功能
     */
    @Test
    @DisplayName("正常场景 - getSlaveHasNoFunctionCodes方法基本功能测试")
    void testGetSlaveHasNoFunctionCodes() {
        // 准备测试数据
        String tenantId = "123456";

        // 执行被测试方法
        Set<String> result = masterSlaveFunctionPrivilegeService.getSlaveHasNoFunctionCodes(tenantId);

        // 验证结果
        assertNotNull(result);
        // 基本验证，不依赖具体的静态配置
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSlaveHasNoFunctionCodes方法，long类型tenantId
     */
    @Test
    @DisplayName("正常场景 - getSlaveHasNoFunctionCodes方法long类型tenantId")
    void testGetSlaveHasNoFunctionCodesWithLongTenantId() {
        // 准备测试数据
        long tenantId = 123456L;

        // 执行被测试方法
        Set<String> result = masterSlaveFunctionPrivilegeService.getSlaveHasNoFunctionCodes(tenantId);

        // 验证结果
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionPrivilege方法，带缓存场景
     */
    @Test
    @DisplayName("正常场景 - checkFunctionPrivilege方法带缓存")
    void testCheckFunctionPrivilegeWithCache() {
        try (MockedStatic<ContextCacheUtil> contextCacheUtilMock = mockStatic(ContextCacheUtil.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {

            // 准备测试数据
            List<String> funcCodeList = Arrays.asList("Object1||Add", "Object1||Edit", "Object2||Delete");

            Map<String, Boolean> cacheResult = new HashMap<>();
            cacheResult.put("Object1||Add", true);

            Map<String, Boolean> clientResult = new HashMap<>();
            clientResult.put("Object1||Edit", true);
            clientResult.put("Object2||Delete", false);

            Map<String, Boolean> expectedFinalResult = new HashMap<>();
            expectedFinalResult.putAll(cacheResult);
            expectedFinalResult.putAll(clientResult);

            // 配置Mock行为
            contextCacheUtilMock.when(() -> ContextCacheUtil.getFunctionPrivilegeCache(
                testAuthContext.getUserId(), funcCodeList)).thenReturn(cacheResult);
            collectionUtilsMock.when(() -> CollectionUtils.notEmpty(cacheResult)).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.notEmpty(any(List.class))).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.nullToEmpty(clientResult)).thenReturn(clientResult);

            when(funcClient.userFuncPermissionCheck(any(AuthContext.class), any(Set.class)))
                    .thenReturn(clientResult);

            // 执行被测试方法
            CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(testAuthContext, funcCodeList);

            // 验证结果
            assertTrue(result.isSuccess());
            assertEquals(expectedFinalResult, result.getResult());
            verify(funcClient, times(1)).userFuncPermissionCheck(any(AuthContext.class), any(Set.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionPrivilege方法，空功能码列表
     */
    @Test
    @DisplayName("边界场景 - checkFunctionPrivilege方法空功能码列表")
    void testCheckFunctionPrivilegeWithEmptyFuncCodeList() {
        try (MockedStatic<ContextCacheUtil> contextCacheUtilMock = mockStatic(ContextCacheUtil.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {

            // 准备测试数据
            List<String> emptyFuncCodeList = Arrays.asList();
            Map<String, Boolean> emptyResult = Maps.newHashMap();

            // 配置Mock行为
            contextCacheUtilMock.when(() -> ContextCacheUtil.getFunctionPrivilegeCache(
                testAuthContext.getUserId(), emptyFuncCodeList)).thenReturn(emptyResult);
            collectionUtilsMock.when(() -> CollectionUtils.notEmpty(emptyResult)).thenReturn(false);
            collectionUtilsMock.when(() -> CollectionUtils.notEmpty(emptyFuncCodeList)).thenReturn(false);

            // 执行被测试方法
            CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(testAuthContext, emptyFuncCodeList);

            // 验证结果
            assertTrue(result.isSuccess());
            assertEquals(emptyResult, result.getResult());
            verifyNoInteractions(funcClient);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionDataPrivilege方法，空功能码列表
     */
    @Test
    @DisplayName("边界场景 - checkFunctionDataPrivilege方法空功能码列表")
    void testCheckFunctionDataPrivilegeWithEmptyFuncCodeList() {
        try (MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {

            // 准备测试数据
            List<String> emptyFuncCodeList = Arrays.asList();
            Map<String, Boolean> emptyResult = Maps.newHashMap();

            // 配置Mock行为
            collectionUtilsMock.when(() -> CollectionUtils.notEmpty(emptyFuncCodeList)).thenReturn(false);

            // 执行被测试方法
            CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionDataPrivilege(testAuthContext, emptyFuncCodeList);

            // 验证结果
            assertTrue(result.isSuccess());
            assertEquals(emptyResult, result.getResult());
            verifyNoInteractions(funcClient);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkFunctionPrivilege方法，已弃用的重载方法
     */
    @Test
    @DisplayName("正常场景 - checkFunctionPrivilege已弃用重载方法")
    void testCheckFunctionPrivilegeDeprecatedMethod() {
        // 准备测试数据
        List<String> funcCodeList = Arrays.asList("Object1||Add", "Object1||Edit");

        CheckFunctionPrivilege.Arg arg = mock(CheckFunctionPrivilege.Arg.class);
        com.facishare.paas.appframework.privilege.dto.AuthContext oldAuthContext =
            mock(com.facishare.paas.appframework.privilege.dto.AuthContext.class);

        when(arg.getAuthContext()).thenReturn(oldAuthContext);
        when(arg.getFuncCodeLists()).thenReturn(funcCodeList);
        when(oldAuthContext.convert()).thenReturn(testAuthContext);

        Map<String, Boolean> expectedResult = new HashMap<>();
        expectedResult.put("Object1||Add", true);
        expectedResult.put("Object1||Edit", false);

        // 配置Mock行为
        when(funcClient.userFuncPermissionCheck(any(AuthContext.class), any(Set.class)))
                .thenReturn(expectedResult);

        // 执行被测试方法
        CheckFunctionPrivilege.Result result = masterSlaveFunctionPrivilegeService.checkFunctionPrivilege(arg);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(expectedResult, result.getResult());
        verify(funcClient, times(1)).userFuncPermissionCheck(any(AuthContext.class), any(Set.class));
    }
}