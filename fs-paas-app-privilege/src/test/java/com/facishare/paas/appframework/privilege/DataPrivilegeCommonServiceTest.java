package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.privilege.dto.PageInfo;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试DataPrivilegeCommonService类的方法
 */
@ExtendWith(MockitoExtension.class)
class DataPrivilegeCommonServiceTest {

    @Mock
    private FsGrayReleaseBiz dataAuthGray;

    @InjectMocks
    private DataPrivilegeCommonService dataPrivilegeCommonService;

    private String testTenantId;

    @BeforeEach
    void setUp() {
        testTenantId = "123456";
        // 注入Mock的灰度发布对象
        ReflectionTestUtils.setField(dataPrivilegeCommonService, "dataAuthGray", dataAuthGray);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enableDataQueryByDescribeAndScopeValidation方法参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideGrayReleaseTestData")
    @DisplayName("参数化测试 - enableDataQueryByDescribeAndScopeValidation方法灰度开关")
    void testEnableDataQueryByDescribeAndScopeValidation_ParameterizedTest(boolean expected) {
        // 配置Mock行为
        when(dataAuthGray.isAllow(anyString(), anyString())).thenReturn(expected);

        // 执行被测试方法
        boolean result = dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(testTenantId);

        // 验证结果
        assertEquals(expected, result);

        // 验证Mock交互
        verify(dataAuthGray, times(1)).isAllow("enableDataQueryByDescribeAndScopeValidation", testTenantId);
    }

    /**
     * 提供灰度发布测试数据
     */
    private static Stream<Arguments> provideGrayReleaseTestData() {
        return Stream.of(
            Arguments.of(true),
            Arguments.of(false)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultPageInfo方法，提供不同的page和size参数的参数化测试
     */
    @ParameterizedTest
    @MethodSource("providePageInfoTestData")
    @DisplayName("参数化测试 - getDefaultPageInfo方法处理不同参数")
    void testGetDefaultPageInfo_ParameterizedTest(Integer page, Integer size, Integer expectedPage, Integer expectedSize) {
        // 执行被测试方法
        PageInfo result = dataPrivilegeCommonService.getDefaultPageInfo(page, size);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPage, result.getCurrentPage());
        assertEquals(expectedSize, result.getPageSize());
        assertEquals(0, result.getTotalPage());
        assertEquals(0, result.getTotal());
    }

    /**
     * 提供分页信息测试数据
     */
    private static Stream<Arguments> providePageInfoTestData() {
        return Stream.of(
            Arguments.of(1, 20, 1, 20),
            Arguments.of(2, 30, 2, 30),
            Arguments.of(null, 10, 1, 10),
            Arguments.of(5, null, 5, 20),
            Arguments.of(null, null, 1, 20)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkEntityIdsByManageGroup方法，检查指定范围内实体的权限的参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideEntityIdsTestData")
    @DisplayName("参数化测试 - checkEntityIdsByManageGroup方法检查实体权限")
    void testCheckEntityIdsByManageGroup_ParameterizedTest(List<String> inputEntityIds, ManageGroup inputManageGroup, List<String> expectedResult) {
        // 执行被测试方法
        List<String> result = dataPrivilegeCommonService.checkEntityIdsByManageGroup(inputEntityIds, inputManageGroup);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult.size(), result.size());
        assertTrue(result.containsAll(expectedResult));
    }

    /**
     * 提供实体ID检查测试数据
     */
    private static Stream<Arguments> provideEntityIdsTestData() {
        return Stream.of(
            Arguments.of(
                Lists.newArrayList("entity1", "entity2"), 
                createManageGroup(false, Lists.newArrayList("entity1", "entity3")), 
                Lists.newArrayList("entity1")
            ),
            Arguments.of(
                Lists.newArrayList("entity1", "entity2"), 
                createManageGroup(true, Lists.newArrayList("entity1", "entity2")), 
                Lists.newArrayList("entity1", "entity2")
            ),
            Arguments.of(
                Lists.newArrayList("entity1", "entity2"), 
                null, 
                Lists.newArrayList()
            ),
            Arguments.of(
                Lists.newArrayList("entity1", "entity2"), 
                createManageGroup(false, Lists.newArrayList()), 
                Lists.newArrayList()
            ),
            Arguments.of(
                Lists.newArrayList(), 
                createManageGroup(false, Lists.newArrayList("entity1", "entity2")), 
                Lists.newArrayList("entity1", "entity2")
            ),
            Arguments.of(
                null, 
                createManageGroup(false, Lists.newArrayList("entity1", "entity2")), 
                Lists.newArrayList("entity1", "entity2")
            )
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkDeptAndOrgIdsByManageScope方法，检查指定范围内部门和组织的权限的参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideDeptAndOrgIdsTestData")
    @DisplayName("参数化测试 - checkDeptAndOrgIdsByManageScope方法检查部门组织权限")
    void testCheckDeptAndOrgIdsByManageScope_ParameterizedTest(Collection<String> inputQueryIds, Set<String> inputDeptAndOrgIds, Set<String> expectedResult) {
        // 执行被测试方法
        Set<String> result = dataPrivilegeCommonService.checkDeptAndOrgIdsByManageScope(inputQueryIds, inputDeptAndOrgIds);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult.size(), result.size());
        assertTrue(result.containsAll(expectedResult));
    }

    /**
     * 提供部门组织ID检查测试数据
     */
    private static Stream<Arguments> provideDeptAndOrgIdsTestData() {
        return Stream.of(
            Arguments.of(
                Lists.newArrayList("dept1", "dept2"), 
                Sets.newHashSet("dept1", "dept3"), 
                Sets.newHashSet("dept1")
            ),
            Arguments.of(
                Lists.newArrayList("dept1", "dept2"), 
                Sets.newHashSet("dept1", "dept2"), 
                Sets.newHashSet("dept1", "dept2")
            ),
            Arguments.of(
                Lists.newArrayList("dept1", "dept2"), 
                Sets.newHashSet(), 
                Sets.newHashSet()
            ),
            Arguments.of(
                Lists.newArrayList("dept1", "dept2"), 
                null, 
                Sets.newHashSet()
            ),
            Arguments.of(
                Lists.newArrayList(), 
                Sets.newHashSet("dept1", "dept2"), 
                Sets.newHashSet("dept1", "dept2")
            ),
            Arguments.of(
                null, 
                Sets.newHashSet("dept1", "dept2"), 
                Sets.newHashSet("dept1", "dept2")
            )
        );
    }

    /**
     * 创建ManageGroup辅助方法
     */
    private static ManageGroup createManageGroup(boolean isAllSupport, List<String> supportApiNames) {
        return new ManageGroup(
                isAllSupport, 
                ManageGroupType.OBJECT, 
                "ParentObj", 
                Sets.newHashSet(supportApiNames)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enableDataQueryByDescribeAndScopeValidation方法，灰度开关开启场景
     */
    @Test
    @DisplayName("正常场景 - enableDataQueryByDescribeAndScopeValidation方法灰度开关开启")
    void testEnableDataQueryByDescribeAndScopeValidation_Enabled() {
        // 配置Mock行为
        when(dataAuthGray.isAllow("enableDataQueryByDescribeAndScopeValidation", testTenantId)).thenReturn(true);

        // 执行被测试方法
        boolean result = dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(testTenantId);

        // 验证结果
        assertTrue(result);

        // 验证Mock交互
        verify(dataAuthGray, times(1)).isAllow("enableDataQueryByDescribeAndScopeValidation", testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDefaultPageInfo方法，传入正常参数
     */
    @Test
    @DisplayName("正常场景 - getDefaultPageInfo方法传入正常参数")
    void testGetDefaultPageInfo_WithValidParams() {
        // 准备测试数据
        Integer page = 2;
        Integer size = 50;

        // 执行被测试方法
        PageInfo result = dataPrivilegeCommonService.getDefaultPageInfo(page, size);

        // 验证结果
        assertNotNull(result);
        assertEquals(page, result.getCurrentPage());
        assertEquals(size, result.getPageSize());
        assertEquals(0, result.getTotalPage());
        assertEquals(0, result.getTotal());
    }
} 