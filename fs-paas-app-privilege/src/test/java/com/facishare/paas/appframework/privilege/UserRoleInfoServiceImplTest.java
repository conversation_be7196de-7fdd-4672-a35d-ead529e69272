package com.facishare.paas.appframework.privilege;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.privilege.dto.GetRolesByUserId;
import com.facishare.paas.appframework.privilege.dto.GetUsersByRole;
import com.facishare.paas.appframework.privilege.dto.QueryUsersByRoles;
import com.facishare.paas.appframework.privilege.dto.QueryUsers;
import com.facishare.paas.appframework.privilege.dto.QueryRoleInfoWithCodes;
import com.facishare.paas.appframework.privilege.dto.QueryRoleCodeByNames;
import com.facishare.paas.appframework.privilege.dto.GetUserRoleInfo;
import com.facishare.paas.appframework.privilege.util.AuthContextExt;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.auth.model.UserRolePojo;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.fxiaoke.paas.auth.factory.ViewClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试UserRoleInfoServiceImpl类的用户角色信息相关方法
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class UserRoleInfoServiceImplTest {

  @Mock
  private RoleClient roleClient;
  
  @Mock
  private ViewClient viewClient;
  
  @Mock
  private UserRoleInfoProxy proxy;
  
  @InjectMocks
  private UserRoleInfoServiceImpl userRoleInfoService;

  private User testUser;
  private User outUser;

  @BeforeEach
  void setUp() {
    testUser = User.builder()
        .tenantId("123456")
        .userId("78910")
        .build();
    testUser.setIsCrmAdmin(Optional.empty());
        
    outUser = User.builder()
        .tenantId("123456")
        .userId("78910")
        .outTenantId("out123")
        .outUserId("out456")
        .build();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getUsersByRole方法，正常获取角色用户
   */
  @Test
  @DisplayName("正常场景 - 测试getUsersByRole方法")
  void testGetUsersByRole_NormalCase() {
    // 准备测试数据
    String roleCode = "ROLE_001";
    
    GetUsersByRole.Result mockResult = mock(GetUsersByRole.Result.class);
    when(mockResult.isSuccess()).thenReturn(true);
    GetUsersByRole.RoleUserPageInfo userResult = mock(GetUsersByRole.RoleUserPageInfo.class);
    when(userResult.getUsers()).thenReturn(Lists.newArrayList("user1", "user2", "user3"));
    when(mockResult.getResult()).thenReturn(userResult);
    
    // 配置Mock行为
    when(proxy.getUsersByRole(any(GetUsersByRole.Arg.class), anyMap())).thenReturn(mockResult);
    
    // 执行被测试方法
    List<String> result = userRoleInfoService.getUsersByRole(testUser, roleCode);
    
    // 验证结果
    assertEquals(3, result.size());
    assertTrue(result.contains("user1"));
    assertTrue(result.contains("user2"));
    assertTrue(result.contains("user3"));
    verify(proxy).getUsersByRole(any(GetUsersByRole.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getUsersByRole方法，失败场景
   */
  @Test
  @DisplayName("异常场景 - 测试getUsersByRole方法失败")
  void testGetUsersByRoleThrowsPermissionError_Failed() {
    // 准备测试数据
    String roleCode = "ROLE_001";
    
    GetUsersByRole.Result mockResult = mock(GetUsersByRole.Result.class);
    when(mockResult.isSuccess()).thenReturn(false);
    when(mockResult.getErrMessage()).thenReturn("获取角色用户失败");
    
    // 配置Mock行为
    when(proxy.getUsersByRole(any(GetUsersByRole.Arg.class), anyMap())).thenReturn(mockResult);
    
    // 执行并验证异常
    PermissionError exception = assertThrows(PermissionError.class, () -> {
      userRoleInfoService.getUsersByRole(testUser, roleCode);
    });
    
    // 验证异常信息
    assertTrue(exception.getMessage().contains("获取角色用户失败"));
    verify(proxy).getUsersByRole(any(GetUsersByRole.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isAdmin方法，超级管理员场景
   */
  @Test
  @DisplayName("正常场景 - 测试isAdmin方法超级管理员")
  void testIsAdmin_SuperAdmin() {
    // 准备测试数据
    User superAdminUser = User.builder()
        .tenantId("123456")
        .userId("-10000")
        .build();

    // 执行被测试方法
    boolean result = userRoleInfoService.isAdmin(superAdminUser);
    
    // 验证结果
    assertTrue(result);
    verifyNoInteractions(roleClient);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isAdmin方法，外部用户场景
   */
  @Test
  @DisplayName("正常场景 - 测试isAdmin方法外部用户")
  void testIsAdmin_OutUser() {
    // 执行被测试方法
    boolean result = userRoleInfoService.isAdmin(outUser);
    
    // 验证结果
    assertFalse(result);
    verifyNoInteractions(roleClient);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isAdmin方法，CRM管理员场景
   */
  @Test
  @DisplayName("正常场景 - 测试isAdmin方法CRM管理员")
  void testIsAdmin_CrmAdmin() {
    try (MockedStatic<AuthContextExt> authContextExtMock = mockStatic(AuthContextExt.class);
         MockedStatic<Sets> setsMock = mockStatic(Sets.class);
         MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
      
      // 准备测试数据
      Map<String, Boolean> adminResults = new HashMap<>();
      adminResults.put(testUser.getUserId(), true);
      
      AuthContextExt mockAuthContextExt = mock(AuthContextExt.class);
      com.facishare.paas.auth.model.AuthContext mockAuthContext = mock(com.facishare.paas.auth.model.AuthContext.class);
      
      // 配置Mock行为
      authContextExtMock.when(() -> AuthContextExt.of(testUser)).thenReturn(mockAuthContextExt);
      when(mockAuthContextExt.getAuthContext()).thenReturn(mockAuthContext);
      Set<String> userIdSet = Sets.newHashSet(testUser.getUserId());
      setsMock.when(() -> Sets.newHashSet(testUser.getUserId())).thenReturn(userIdSet);
      collectionUtilsMock.when(() -> CollectionUtils.nullToEmpty(adminResults)).thenReturn(adminResults);
      
      when(roleClient.checkCrmManager(mockAuthContext, userIdSet)).thenReturn(adminResults);
      
      // 执行被测试方法
      boolean result = userRoleInfoService.isAdmin(testUser);
      
      // 验证结果
      assertTrue(result);
      assertTrue(testUser.getIsCrmAdmin().isPresent());
      assertTrue(testUser.getIsCrmAdmin().get());
      verify(roleClient).checkCrmManager(mockAuthContext, userIdSet);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getUserRole方法，正常获取用户角色
   */
  @Test
  @DisplayName("正常场景 - 测试getUserRole方法")
  void testGetUserRole_NormalCase() {
    try (MockedStatic<AuthContextExt> authContextExtMock = mockStatic(AuthContextExt.class);
         MockedStatic<Lists> listsMock = mockStatic(Lists.class)) {
      
      // 准备测试数据
      Set<String> roleCodes = Sets.newHashSet("ROLE_001", "ROLE_002");
      List<String> expectedRoles = Lists.newArrayList("ROLE_001", "ROLE_002");
      
      AuthContextExt mockAuthContextExt = mock(AuthContextExt.class);
      com.facishare.paas.auth.model.AuthContext mockAuthContext = mock(com.facishare.paas.auth.model.AuthContext.class);
      
      // 配置Mock行为
      authContextExtMock.when(() -> AuthContextExt.of(testUser)).thenReturn(mockAuthContextExt);
      when(mockAuthContextExt.getAuthContext()).thenReturn(mockAuthContext);
      listsMock.when(() -> Lists.newArrayList(roleCodes)).thenReturn(expectedRoles);
      
      when(roleClient.queryRoleCodeByUserId(mockAuthContext)).thenReturn(roleCodes);
      
      // 执行被测试方法
      List<String> result = userRoleInfoService.getUserRole(testUser);
      
      // 验证结果
      assertEquals(expectedRoles, result);
      verify(roleClient).queryRoleCodeByUserId(mockAuthContext);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getUserRole方法，AuthException异常
   */
  @Test
  @DisplayName("异常场景 - 测试getUserRole方法AuthException")
  void testGetUserRoleThrowsPermissionError_AuthException() {
    try (MockedStatic<AuthContextExt> authContextExtMock = mockStatic(AuthContextExt.class);
         MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      
      // 准备测试数据
      AuthException authException = new AuthException(401, "认证异常");
      
      AuthContextExt mockAuthContextExt = mock(AuthContextExt.class);
      com.facishare.paas.auth.model.AuthContext mockAuthContext = mock(com.facishare.paas.auth.model.AuthContext.class);
      
      // 配置Mock行为
      authContextExtMock.when(() -> AuthContextExt.of(testUser)).thenReturn(mockAuthContextExt);
      when(mockAuthContextExt.getAuthContext()).thenReturn(mockAuthContext);
      i18nMock.when(() -> I18N.text(I18NKey.GET_ROLE_LIST_FAIL)).thenReturn("获取角色列表失败");
      
      when(roleClient.queryRoleCodeByUserId(mockAuthContext)).thenThrow(authException);
      
      // 执行并验证异常
      PermissionError exception = assertThrows(PermissionError.class, () -> {
        userRoleInfoService.getUserRole(testUser);
      });
      
      // 验证异常信息
      assertTrue(exception.getMessage().contains("获取角色列表失败"));
      verify(roleClient).queryRoleCodeByUserId(mockAuthContext);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getDefaultRoleCode方法，正常获取默认角色
   */
  @Test
  @DisplayName("正常场景 - 测试getDefaultRoleCode方法")
  void testGetDefaultRoleCode_NormalCase() {
    try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class)) {
      
      // 准备测试数据
      List<GetRolesByUserId.UserRole> userRoles = Lists.newArrayList();
      GetRolesByUserId.UserRole defaultRole = new GetRolesByUserId.UserRole();
      defaultRole.setRoleCode("DEFAULT_ROLE");
      defaultRole.setDefaultRole(true);
      userRoles.add(defaultRole);
      
      GetRolesByUserId.UserRole normalRole = new GetRolesByUserId.UserRole();
      normalRole.setRoleCode("NORMAL_ROLE");
      normalRole.setDefaultRole(false);
      userRoles.add(normalRole);
      
      // 配置Mock行为
      grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.USER_DEFAULT_ROLE_CODE_LOCAL_CACHE_GRAY, testUser.getTenantId())).thenReturn(false);
      
      // Mock getRoleInfoByUser方法
      UserRoleInfoServiceImpl spyService = spy(userRoleInfoService);
      doReturn(userRoles).when(spyService).getRoleInfoByUser(testUser);
      
      // 执行被测试方法
      Optional<String> result = spyService.getDefaultRoleCode(testUser);
      
      // 验证结果
      assertTrue(result.isPresent());
      assertEquals("DEFAULT_ROLE", result.get());
      verify(spyService).getRoleInfoByUser(testUser);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getDefaultRoleCode方法，使用缓存场景
   */
  @Test
  @DisplayName("正常场景 - 测试getDefaultRoleCode方法使用缓存")
  void testGetDefaultRoleCode_WithCache() {
    try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class);
         MockedStatic<CacheContext> cacheContextMock = mockStatic(CacheContext.class);
         MockedStatic<StringUtils> stringUtilsMock = mockStatic(StringUtils.class)) {
      
      // 准备测试数据
      String cachedRoleCode = "CACHED_ROLE";
      CacheContext mockCacheContext = mock(CacheContext.class);
      
      // 配置Mock行为
      grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.USER_DEFAULT_ROLE_CODE_LOCAL_CACHE_GRAY, testUser.getTenantId())).thenReturn(true);
      cacheContextMock.when(CacheContext::getContext).thenReturn(mockCacheContext);
      when(mockCacheContext.getCache(anyString())).thenReturn(cachedRoleCode);
      stringUtilsMock.when(() -> StringUtils.isNotBlank(cachedRoleCode)).thenReturn(true);
      
      // 执行被测试方法
      Optional<String> result = userRoleInfoService.getDefaultRoleCode(testUser);
      
      // 验证结果
      assertTrue(result.isPresent());
      assertEquals(cachedRoleCode, result.get());
      verify(mockCacheContext).getCache(anyString());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRoleInfoByUser方法，内部用户场景
   */
  @Test
  @DisplayName("正常场景 - 测试getRoleInfoByUser方法内部用户")
  void testGetRoleInfoByUser_InternalUser() {
    // 准备测试数据
    List<GetRolesByUserId.UserRole> expectedRoles = Lists.newArrayList();
    GetRolesByUserId.UserRole userRole = new GetRolesByUserId.UserRole();
    userRole.setRoleCode("ROLE_001");
    userRole.setDefaultRole(true);
    expectedRoles.add(userRole);
    
    GetRolesByUserId.Result mockResult = mock(GetRolesByUserId.Result.class);
    when(mockResult.isSuccess()).thenReturn(true);
    when(mockResult.getResult()).thenReturn(expectedRoles);
    
    // 配置Mock行为
    when(proxy.GetRolesByUserId(any(GetRolesByUserId.Arg.class), anyMap())).thenReturn(mockResult);
    
    // 执行被测试方法
    List<GetRolesByUserId.UserRole> result = userRoleInfoService.getRoleInfoByUser(testUser);
    
    // 验证结果
    assertEquals(expectedRoles, result);
    verify(proxy).GetRolesByUserId(any(GetRolesByUserId.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getRoleInfoByUser方法，外部用户场景
   */
  @Test
  @DisplayName("正常场景 - 测试getRoleInfoByUser方法外部用户")
  void testGetRoleInfoByUser_OutUser() {
    try (MockedStatic<AuthContextExt> authContextExtMock = mockStatic(AuthContextExt.class);
         MockedStatic<Sets> setsMock = mockStatic(Sets.class)) {
      
      // 准备测试数据
      List<UserRolePojo> userRolePojos = Lists.newArrayList();
      UserRolePojo userRolePojo = new UserRolePojo();
      userRolePojo.setRoleCode("OUT_ROLE");
      userRolePojo.setDefaultRole(true);
      userRolePojo.setAppId("app123");
      userRolePojo.setOrgId("org123");
      userRolePojos.add(userRolePojo);
      
      AuthContextExt mockAuthContextExt = mock(AuthContextExt.class);
      com.facishare.paas.auth.model.AuthContext mockAuthContext = mock(com.facishare.paas.auth.model.AuthContext.class);
      
      // 配置Mock行为
      authContextExtMock.when(() -> AuthContextExt.of(outUser)).thenReturn(mockAuthContextExt);
      when(mockAuthContextExt.getAuthContext()).thenReturn(mockAuthContext);
      Set<String> outUserIdSet = Sets.newHashSet(outUser.getOutUserId());
      setsMock.when(() -> Sets.newHashSet(outUser.getOutUserId())).thenReturn(outUserIdSet);
      
      when(roleClient.queryUserRoleByUserIds(mockAuthContext, outUserIdSet)).thenReturn(userRolePojos);
      
      // 执行被测试方法
      List<GetRolesByUserId.UserRole> result = userRoleInfoService.getRoleInfoByUser(outUser);
      
      // 验证结果
      assertEquals(1, result.size());
      assertEquals("OUT_ROLE", result.get(0).getRoleCode());
      assertTrue(result.get(0).getDefaultRole());
      assertEquals("app123", result.get(0).getAppId());
      assertEquals("org123", result.get(0).getOrgId());
      verify(roleClient).queryUserRoleByUserIds(mockAuthContext, outUserIdSet);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getMainRoleLayoutAPIName方法，正常获取布局API名称
   */
  @Test
  @DisplayName("正常场景 - 测试getMainRoleLayoutAPIName方法")
  void testGetMainRoleLayoutAPIName_NormalCase() {
    try (MockedStatic<AuthContextExt> authContextExtMock = mockStatic(AuthContextExt.class)) {

      // 准备测试数据
      String objectAPIName = "TestObj";
      String recordType = "standard";
      String expectedViewId = "view123";

      List<RoleViewPojo> roleViews = Lists.newArrayList();
      RoleViewPojo roleView = new RoleViewPojo();
      roleView.setViewId(expectedViewId);
      roleViews.add(roleView);

      AuthContextExt mockAuthContextExt = mock(AuthContextExt.class);
      com.facishare.paas.auth.model.AuthContext mockAuthContext = mock(com.facishare.paas.auth.model.AuthContext.class);

      // 配置Mock行为
      authContextExtMock.when(() -> AuthContextExt.of(testUser)).thenReturn(mockAuthContextExt);
      when(mockAuthContextExt.getAuthContext()).thenReturn(mockAuthContext);

      when(viewClient.findDefaultRoleView(mockAuthContext, objectAPIName, recordType, LayoutTypes.DETAIL)).thenReturn(roleViews);

      // 执行被测试方法
      Optional<String> result = userRoleInfoService.getMainRoleLayoutAPIName(testUser, objectAPIName, recordType);

      // 验证结果
      assertTrue(result.isPresent());
      assertEquals(expectedViewId, result.get());
      verify(viewClient).findDefaultRoleView(mockAuthContext, objectAPIName, recordType, LayoutTypes.DETAIL);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getMainRoleLayoutAPIName方法，AuthException异常
   */
  @Test
  @DisplayName("异常场景 - 测试getMainRoleLayoutAPIName方法AuthException")
  void testGetMainRoleLayoutAPINameThrowsPermissionError_AuthException() {
    try (MockedStatic<AuthContextExt> authContextExtMock = mockStatic(AuthContextExt.class);
         MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {

      // 准备测试数据
      String objectAPIName = "TestObj";
      String recordType = "standard";
      AuthException authException = new AuthException(401, "认证异常");

      AuthContextExt mockAuthContextExt = mock(AuthContextExt.class);
      com.facishare.paas.auth.model.AuthContext mockAuthContext = mock(com.facishare.paas.auth.model.AuthContext.class);

      // 配置Mock行为
      authContextExtMock.when(() -> AuthContextExt.of(testUser)).thenReturn(mockAuthContextExt);
      when(mockAuthContextExt.getAuthContext()).thenReturn(mockAuthContext);
      i18nMock.when(() -> I18N.text(I18NKey.GET_ROLE_LIST_FAIL)).thenReturn("获取角色列表失败");

      when(viewClient.findDefaultRoleView(mockAuthContext, objectAPIName, recordType, LayoutTypes.DETAIL)).thenThrow(authException);

      // 执行并验证异常
      PermissionError exception = assertThrows(PermissionError.class, () -> {
        userRoleInfoService.getMainRoleLayoutAPIName(testUser, objectAPIName, recordType);
      });

      // 验证异常信息
      assertTrue(exception.getMessage().contains("获取角色列表失败"));
      verify(viewClient).findDefaultRoleView(mockAuthContext, objectAPIName, recordType, LayoutTypes.DETAIL);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryRoleUsersByRoles方法，正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试queryRoleUsersByRoles方法")
  void testQueryRoleUsersByRoles_Success() {
    // 准备测试数据
    List<String> roles = Lists.newArrayList("ROLE_001", "ROLE_002");

    QueryUsersByRoles.Result mockResult = mock(QueryUsersByRoles.Result.class);
    when(mockResult.isSuccess()).thenReturn(true);
    Map<String, Object> resultMap = Maps.newHashMap();
    resultMap.put("ROLE_001", Lists.newArrayList("user1", "user2"));
    resultMap.put("ROLE_002", Lists.newArrayList("user3"));
    when(mockResult.getResult()).thenReturn(resultMap);

    // 配置Mock行为
    when(proxy.queryRoleUsersByRoles(any(QueryUsersByRoles.Arg.class), anyMap())).thenReturn(mockResult);

    // 执行被测试方法
    List<String> result = userRoleInfoService.queryRoleUsersByRoles(testUser, roles);

    // 验证结果
    assertEquals(3, result.size());
    assertTrue(result.contains("user1"));
    assertTrue(result.contains("user2"));
    assertTrue(result.contains("user3"));
    verify(proxy).queryRoleUsersByRoles(any(QueryUsersByRoles.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryRoleUsersByRoles方法，空结果场景
   */
  @Test
  @DisplayName("边界场景 - 测试queryRoleUsersByRoles方法空结果")
  void testQueryRoleUsersByRoles_EmptyResult() {
    // 准备测试数据
    List<String> roles = Lists.newArrayList("ROLE_001");

    QueryUsersByRoles.Result mockResult = mock(QueryUsersByRoles.Result.class);
    when(mockResult.isSuccess()).thenReturn(true);
    Map<String, Object> emptyResultMap = Maps.newHashMap();
    when(mockResult.getResult()).thenReturn(emptyResultMap);

    // 配置Mock行为
    when(proxy.queryRoleUsersByRoles(any(QueryUsersByRoles.Arg.class), anyMap())).thenReturn(mockResult);

    // 执行被测试方法
    List<String> result = userRoleInfoService.queryRoleUsersByRoles(testUser, roles);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
    verify(proxy).queryRoleUsersByRoles(any(QueryUsersByRoles.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryUsers方法，正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试queryUsers方法")
  void testQueryUsers_Success() {
    // 准备测试数据
    String roleCode = "ROLE_001";
    Set<String> userIdSet = Sets.newHashSet("user1", "user2");

    QueryUsers.Result mockResult = mock(QueryUsers.Result.class);
    when(mockResult.isSuccess()).thenReturn(true);
    Map<String, List<String>> resultMap = Maps.newHashMap();
    resultMap.put("users", Lists.newArrayList("user1", "user2"));
    when(mockResult.getResult()).thenReturn(resultMap);

    // 配置Mock行为
    when(proxy.queryUsers(any(QueryUsers.Arg.class), anyMap())).thenReturn(mockResult);

    // 执行被测试方法
    List<String> result = userRoleInfoService.queryUsers(testUser, roleCode, userIdSet);

    // 验证结果
    assertEquals(2, result.size());
    assertTrue(result.contains("user1"));
    assertTrue(result.contains("user2"));
    verify(proxy).queryUsers(any(QueryUsers.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryRoleNameByRoleCode方法，正常场景
   */
  @Test
  @DisplayName("正常场景 - 测试queryRoleNameByRoleCode方法")
  void testQueryRoleNameByRoleCode_Success() {
    // 准备测试数据
    List<String> roleIds = Lists.newArrayList("ROLE_001", "ROLE_002");

    List<GetUserRoleInfo.RoleInfo> roleInfos = Lists.newArrayList();
    GetUserRoleInfo.RoleInfo roleInfo1 = new GetUserRoleInfo.RoleInfo();
    roleInfo1.setRoleCode("ROLE_001");
    roleInfo1.setRoleName("角色1");
    roleInfos.add(roleInfo1);

    GetUserRoleInfo.RoleInfo roleInfo2 = new GetUserRoleInfo.RoleInfo();
    roleInfo2.setRoleCode("ROLE_002");
    roleInfo2.setRoleName("角色2");
    roleInfos.add(roleInfo2);

    // Mock queryRoleInfoByRoleCode方法
    UserRoleInfoServiceImpl spyService = spy(userRoleInfoService);
    doReturn(roleInfos).when(spyService).queryRoleInfoByRoleCode(testUser, roleIds);

    // 执行被测试方法
    Map<String, String> result = spyService.queryRoleNameByRoleCode(testUser, roleIds);

    // 验证结果
    assertEquals(2, result.size());
    assertEquals("角色1", result.get("ROLE_001"));
    assertEquals("角色2", result.get("ROLE_002"));
    verify(spyService).queryRoleInfoByRoleCode(testUser, roleIds);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getMainRoleLayoutAPIName方法，带viewType参数
   */
  @Test
  @DisplayName("正常场景 - 测试getMainRoleLayoutAPIName方法带viewType参数")
  void testGetMainRoleLayoutAPINameWithViewType_NormalCase() {
    try (MockedStatic<AuthContextExt> authContextExtMock = mockStatic(AuthContextExt.class)) {

      // 准备测试数据
      String objectAPIName = "TestObj";
      String recordType = "standard";
      String viewType = "LIST";
      String expectedViewId = "view456";

      List<RoleViewPojo> roleViews = Lists.newArrayList();
      RoleViewPojo roleView = new RoleViewPojo();
      roleView.setViewId(expectedViewId);
      roleViews.add(roleView);

      AuthContextExt mockAuthContextExt = mock(AuthContextExt.class);
      com.facishare.paas.auth.model.AuthContext mockAuthContext = mock(com.facishare.paas.auth.model.AuthContext.class);

      // 配置Mock行为
      authContextExtMock.when(() -> AuthContextExt.of(testUser)).thenReturn(mockAuthContextExt);
      when(mockAuthContextExt.getAuthContext()).thenReturn(mockAuthContext);

      when(viewClient.findDefaultRoleView(mockAuthContext, objectAPIName, recordType, viewType)).thenReturn(roleViews);

      // 执行被测试方法
      Optional<String> result = userRoleInfoService.getMainRoleLayoutAPIName(testUser, objectAPIName, recordType, viewType);

      // 验证结果
      assertTrue(result.isPresent());
      assertEquals(expectedViewId, result.get());
      verify(viewClient).findDefaultRoleView(mockAuthContext, objectAPIName, recordType, viewType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getMainRoleLayoutByRecordType方法，正常获取布局
   */
  @Test
  @DisplayName("正常场景 - 测试getMainRoleLayoutByRecordType方法")
  void testGetMainRoleLayoutByRecordType_NormalCase() {
    try (MockedStatic<AuthContextExt> authContextExtMock = mockStatic(AuthContextExt.class)) {

      // 准备测试数据
      String objectAPIName = "TestObj";
      String viewType = "LIST";

      List<RoleViewPojo> expectedRoleViews = Lists.newArrayList();
      RoleViewPojo roleView1 = new RoleViewPojo();
      roleView1.setViewId("view1");
      RoleViewPojo roleView2 = new RoleViewPojo();
      roleView2.setViewId("view2");
      expectedRoleViews.add(roleView1);
      expectedRoleViews.add(roleView2);

      AuthContextExt mockAuthContextExt = mock(AuthContextExt.class);
      com.facishare.paas.auth.model.AuthContext mockAuthContext = mock(com.facishare.paas.auth.model.AuthContext.class);

      // 配置Mock行为
      authContextExtMock.when(() -> AuthContextExt.of(testUser)).thenReturn(mockAuthContextExt);
      when(mockAuthContextExt.getAuthContext()).thenReturn(mockAuthContext);

      when(viewClient.findDefaultRoleView(mockAuthContext, objectAPIName, "", viewType)).thenReturn(expectedRoleViews);

      // 执行被测试方法
      List<RoleViewPojo> result = userRoleInfoService.getMainRoleLayoutByRecordType(testUser, objectAPIName, viewType);

      // 验证结果
      assertEquals(expectedRoleViews, result);
      verify(viewClient).findDefaultRoleView(mockAuthContext, objectAPIName, "", viewType);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryRoleUsersByRoles方法，正常获取角色用户
   */
  @Test
  @DisplayName("正常场景 - 测试queryRoleUsersByRoles方法")
  void testQueryRoleUsersByRoles_NormalCase() {
    // 准备测试数据
    List<String> roles = Lists.newArrayList("ROLE_001", "ROLE_002");

    Map<String, Object> resultMap = new HashMap<>();
    resultMap.put("ROLE_001", Lists.newArrayList("user1", "user2"));
    resultMap.put("ROLE_002", Lists.newArrayList("user3", "user4"));

    QueryUsersByRoles.Result mockResult = mock(QueryUsersByRoles.Result.class);
    when(mockResult.getResult()).thenReturn(resultMap);

    // 配置Mock行为
    when(proxy.queryRoleUsersByRoles(any(QueryUsersByRoles.Arg.class), anyMap())).thenReturn(mockResult);

    // 执行被测试方法
    List<String> result = userRoleInfoService.queryRoleUsersByRoles(testUser, roles);

    // 验证结果
    assertEquals(4, result.size());
    assertTrue(result.contains("user1"));
    assertTrue(result.contains("user2"));
    assertTrue(result.contains("user3"));
    assertTrue(result.contains("user4"));
    verify(proxy).queryRoleUsersByRoles(any(QueryUsersByRoles.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryUsers方法，正常获取用户
   */
  @Test
  @DisplayName("正常场景 - 测试queryUsers方法")
  void testQueryUsers_NormalCase() {
    // 准备测试数据
    String roleCode = "ROLE_001";
    Set<String> userIdSet = Sets.newHashSet("user1", "user2", "user3");

    Map<String, List<String>> resultMap = new HashMap<>();
    resultMap.put("users", Lists.newArrayList("user1", "user2"));

    QueryUsers.Result mockResult = mock(QueryUsers.Result.class);
    when(mockResult.isSuccess()).thenReturn(true);
    when(mockResult.getResult()).thenReturn(resultMap);

    // 配置Mock行为
    when(proxy.queryUsers(any(QueryUsers.Arg.class), anyMap())).thenReturn(mockResult);

    // 执行被测试方法
    List<String> result = userRoleInfoService.queryUsers(testUser, roleCode, userIdSet);

    // 验证结果
    assertEquals(2, result.size());
    assertTrue(result.contains("user1"));
    assertTrue(result.contains("user2"));
    verify(proxy).queryUsers(any(QueryUsers.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryUsers方法，失败场景
   */
  @Test
  @DisplayName("异常场景 - 测试queryUsers方法失败")
  void testQueryUsersThrowsPermissionError_Failed() {
    // 准备测试数据
    String roleCode = "ROLE_001";
    Set<String> userIdSet = Sets.newHashSet("user1", "user2");

    QueryUsers.Result mockResult = mock(QueryUsers.Result.class);
    when(mockResult.isSuccess()).thenReturn(false);
    when(mockResult.getErrMessage()).thenReturn("查询用户失败");

    // 配置Mock行为
    when(proxy.queryUsers(any(QueryUsers.Arg.class), anyMap())).thenReturn(mockResult);

    // 执行并验证异常
    PermissionError exception = assertThrows(PermissionError.class, () -> {
      userRoleInfoService.queryUsers(testUser, roleCode, userIdSet);
    });

    // 验证异常信息
    assertTrue(exception.getMessage().contains("查询用户失败"));
    verify(proxy).queryUsers(any(QueryUsers.Arg.class), anyMap());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryRoleInfoByRoleCode方法，正常获取角色信息
   */
  @Test
  @DisplayName("正常场景 - 测试queryRoleInfoByRoleCode方法")
  void testQueryRoleInfoByRoleCode_NormalCase() {
    try (MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {

      // 准备测试数据
      List<String> roleIds = Lists.newArrayList("ROLE_001", "ROLE_002");

      List<GetUserRoleInfo.RoleInfo> expectedRoles = Lists.newArrayList();
      GetUserRoleInfo.RoleInfo roleInfo1 = new GetUserRoleInfo.RoleInfo();
      roleInfo1.setRoleCode("ROLE_001");
      roleInfo1.setRoleName("角色1");
      GetUserRoleInfo.RoleInfo roleInfo2 = new GetUserRoleInfo.RoleInfo();
      roleInfo2.setRoleCode("ROLE_002");
      roleInfo2.setRoleName("角色2");
      expectedRoles.add(roleInfo1);
      expectedRoles.add(roleInfo2);

      QueryRoleInfoWithCodes.Result mockResult = mock(QueryRoleInfoWithCodes.Result.class);
      when(mockResult.isSuccess()).thenReturn(true);
      when(mockResult.getRoles()).thenReturn(expectedRoles);

      // 配置Mock行为
      collectionUtilsMock.when(() -> CollectionUtils.empty(roleIds)).thenReturn(false);
      collectionUtilsMock.when(() -> CollectionUtils.nullToEmpty(expectedRoles)).thenReturn(expectedRoles);
      when(proxy.queryRoleInfoWithCodes(any(QueryRoleInfoWithCodes.Arg.class), anyMap())).thenReturn(mockResult);

      // 执行被测试方法
      List<GetUserRoleInfo.RoleInfo> result = userRoleInfoService.queryRoleInfoByRoleCode(testUser, roleIds);

      // 验证结果
      assertEquals(expectedRoles, result);
      verify(proxy).queryRoleInfoWithCodes(any(QueryRoleInfoWithCodes.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryRoleInfoByRoleCode方法，空角色ID列表
   */
  @Test
  @DisplayName("边界场景 - 测试queryRoleInfoByRoleCode方法空角色ID列表")
  void testQueryRoleInfoByRoleCode_EmptyRoleIds() {
    try (MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class);
         MockedStatic<Lists> listsMock = mockStatic(Lists.class)) {

      // 准备测试数据
      List<String> emptyRoleIds = Lists.newArrayList();
      List<GetUserRoleInfo.RoleInfo> expectedEmptyResult = Lists.newArrayList();

      // 配置Mock行为
      collectionUtilsMock.when(() -> CollectionUtils.empty(emptyRoleIds)).thenReturn(true);
      listsMock.when(Lists::newArrayList).thenReturn(expectedEmptyResult);

      // 执行被测试方法
      List<GetUserRoleInfo.RoleInfo> result = userRoleInfoService.queryRoleInfoByRoleCode(testUser, emptyRoleIds);

      // 验证结果
      assertEquals(expectedEmptyResult, result);
      verifyNoInteractions(proxy);
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryRoleInfoByRoleCode方法，失败场景
   */
  @Test
  @DisplayName("异常场景 - 测试queryRoleInfoByRoleCode方法失败")
  void testQueryRoleInfoByRoleCodeThrowsPermissionError_Failed() {
    try (MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {

      // 准备测试数据
      List<String> roleIds = Lists.newArrayList("ROLE_001");

      QueryRoleInfoWithCodes.Result mockResult = mock(QueryRoleInfoWithCodes.Result.class);
      when(mockResult.isSuccess()).thenReturn(false);
      when(mockResult.getErrMessage()).thenReturn("查询角色信息失败");

      // 配置Mock行为
      collectionUtilsMock.when(() -> CollectionUtils.empty(roleIds)).thenReturn(false);
      when(proxy.queryRoleInfoWithCodes(any(QueryRoleInfoWithCodes.Arg.class), anyMap())).thenReturn(mockResult);

      // 执行并验证异常
      PermissionError exception = assertThrows(PermissionError.class, () -> {
        userRoleInfoService.queryRoleInfoByRoleCode(testUser, roleIds);
      });

      // 验证异常信息
      assertTrue(exception.getMessage().contains("查询角色信息失败"));
      verify(proxy).queryRoleInfoWithCodes(any(QueryRoleInfoWithCodes.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试queryRoleNameByRoleCode方法，正常获取角色名称映射
   */
  @Test
  @DisplayName("正常场景 - 测试queryRoleNameByRoleCode方法")
  void testQueryRoleNameByRoleCode_NormalCase() {
    // 准备测试数据
    List<String> roleIds = Lists.newArrayList("ROLE_001", "ROLE_002");

    List<GetUserRoleInfo.RoleInfo> roleInfos = Lists.newArrayList();
    GetUserRoleInfo.RoleInfo roleInfo1 = new GetUserRoleInfo.RoleInfo();
    roleInfo1.setRoleCode("ROLE_001");
    roleInfo1.setRoleName("角色1");
    GetUserRoleInfo.RoleInfo roleInfo2 = new GetUserRoleInfo.RoleInfo();
    roleInfo2.setRoleCode("ROLE_002");
    roleInfo2.setRoleName("角色2");
    roleInfos.add(roleInfo1);
    roleInfos.add(roleInfo2);

    // Mock queryRoleInfoByRoleCode方法
    UserRoleInfoServiceImpl spyService = spy(userRoleInfoService);
    doReturn(roleInfos).when(spyService).queryRoleInfoByRoleCode(testUser, roleIds);

    // 执行被测试方法
    Map<String, String> result = spyService.queryRoleNameByRoleCode(testUser, roleIds);

    // 验证结果
    assertEquals(2, result.size());
    assertEquals("角色1", result.get("ROLE_001"));
    assertEquals("角色2", result.get("ROLE_002"));
    verify(spyService).queryRoleInfoByRoleCode(testUser, roleIds);
  }
}
