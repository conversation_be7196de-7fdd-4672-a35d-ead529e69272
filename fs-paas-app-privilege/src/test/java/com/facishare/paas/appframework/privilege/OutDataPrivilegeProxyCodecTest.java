package com.facishare.paas.appframework.privilege;

import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OutDataPrivilegeProxy.Codec 单元测试
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
class OutDataPrivilegeProxyCodecTest {

  private OutDataPrivilegeProxy.Codec codec;

  @BeforeEach
  void setUp() {
    codec = new OutDataPrivilegeProxy.Codec();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试encodeArg方法，正常对象序列化
   */
  @Test
  @DisplayName("正常场景 - 测试encodeArg方法对象序列化")
  void testEncodeArg_Success() {
    // 准备测试数据
    Map<String, Object> testObj = Maps.newHashMap();
    testObj.put("appId", "CRM");
    testObj.put("objectApiName", "TestObject");
    testObj.put("upstreamTenantId", "tenant123");
    
    // 执行被测试方法
    byte[] result = codec.encodeArg(testObj);
    
    // 验证结果
    assertNotNull(result);
    assertTrue(result.length > 0);
    
    // 验证序列化的内容包含预期字段
    String jsonString = new String(result);
    assertTrue(jsonString.contains("CRM"));
    assertTrue(jsonString.contains("TestObject"));
    assertTrue(jsonString.contains("tenant123"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试encodeArg方法，null对象处理
   */
  @Test
  @DisplayName("边界场景 - 测试encodeArg方法null对象")
  void testEncodeArg_NullObject() {
    // 执行被测试方法
    byte[] result = codec.encodeArg(null);
    
    // 验证结果
    assertNotNull(result);
    assertEquals("null", new String(result));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，成功响应解析
   */
  @Test
  @DisplayName("正常场景 - 测试decodeResult方法成功响应")
  void testDecodeResult_Success() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 0,\n" +
      "  \"errMsg\": \"success\",\n" +
      "  \"data\": \"1\"\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法
    Integer result = codec.decodeResult(200, headers, responseBytes, Integer.class);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.intValue());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，字符串类型响应
   */
  @Test
  @DisplayName("正常场景 - 测试decodeResult方法字符串响应")
  void testDecodeResult_StringResponse() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 0,\n" +
      "  \"errMsg\": \"success\",\n" +
      "  \"data\": \"\\\"test_string\\\"\"\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法
    String result = codec.decodeResult(200, headers, responseBytes, String.class);
    
    // 验证结果
    assertNotNull(result);
    assertEquals("test_string", result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，HTTP状态码非200
   */
  @Test
  @DisplayName("异常场景 - 测试decodeResult方法HTTP状态码非200")
  void testDecodeResult_NonSuccessStatusCode() {
    // 准备测试数据
    String errorResponse = "Internal Server Error";
    byte[] responseBytes = errorResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
      codec.decodeResult(500, headers, responseBytes, Integer.class);
    });
    
    // 验证异常信息
    assertTrue(exception.getMessage().contains("decode error,body:"));
    assertTrue(exception.getMessage().contains("Internal Server Error"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，业务错误码非0且有错误消息
   */
  @Test
  @DisplayName("异常场景 - 测试decodeResult方法业务错误有消息")
  void testDecodeResult_BusinessErrorWithMessage() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 1001,\n" +
      "  \"errMsg\": \"权限不足\",\n" +
      "  \"data\": null\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RestProxyBusinessException exception = assertThrows(RestProxyBusinessException.class, () -> {
      codec.decodeResult(200, headers, responseBytes, Integer.class);
    });
    
    // 验证异常信息
    assertEquals(1001, exception.getCode());
    assertEquals("权限不足", exception.getMessage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，业务错误码非0且无错误消息
   */
  @Test
  @DisplayName("异常场景 - 测试decodeResult方法业务错误无消息")
  void testDecodeResult_BusinessErrorWithoutMessage() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 1002,\n" +
      "  \"data\": null\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RestProxyBusinessException exception = assertThrows(RestProxyBusinessException.class, () -> {
      codec.decodeResult(200, headers, responseBytes, Integer.class);
    });
    
    // 验证异常信息
    assertEquals(1002, exception.getCode());
    assertEquals("Empty Error Message", exception.getMessage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，无效JSON格式
   */
  @Test
  @DisplayName("异常场景 - 测试decodeResult方法无效JSON")
  void testDecodeResult_InvalidJson() {
    // 准备测试数据
    String invalidJson = "invalid json content";
    byte[] responseBytes = invalidJson.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
      codec.decodeResult(200, headers, responseBytes, Integer.class);
    });
    
    // 验证异常信息
    assertTrue(exception.getMessage().contains("decode error"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，空响应体
   */
  @Test
  @DisplayName("边界场景 - 测试decodeResult方法空响应体")
  void testDecodeResult_EmptyResponse() {
    // 准备测试数据
    byte[] responseBytes = null;
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法并验证异常
    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
      codec.decodeResult(500, headers, responseBytes, Integer.class);
    });
    
    // 验证异常信息
    assertTrue(exception.getMessage().contains("decode error,body:"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试decodeResult方法，复杂对象响应
   */
  @Test
  @DisplayName("正常场景 - 测试decodeResult方法复杂对象响应")
  void testDecodeResult_ComplexObjectResponse() {
    // 准备测试数据
    String jsonResponse = "{\n" +
      "  \"errCode\": 0,\n" +
      "  \"errMsg\": \"success\",\n" +
      "  \"data\": \"{\\\"id\\\":\\\"123\\\",\\\"name\\\":\\\"test\\\",\\\"value\\\":456}\"\n" +
      "}";
    byte[] responseBytes = jsonResponse.getBytes();
    Map<String, List<String>> headers = Maps.newHashMap();
    
    // 执行被测试方法
    Map result = codec.decodeResult(200, headers, responseBytes, Map.class);
    
    // 验证结果
    assertNotNull(result);
    assertEquals("123", result.get("id"));
    assertEquals("test", result.get("name"));
    assertEquals(456, result.get("value"));
  }
}
