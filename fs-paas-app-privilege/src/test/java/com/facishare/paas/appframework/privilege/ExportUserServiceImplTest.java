package com.facishare.paas.appframework.privilege;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.ListUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试ExportUserServiceImpl类的用户导出相关方法
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ExportUserServiceImplTest {

  @Mock
  private GDSHandler gdsService;
  
  @Mock
  private UserRoleInfoProxy userRoleInfoProxy;
  
  @Mock
  private OrgService orgService;
  
  @Mock
  private JudgeMangePrivilegeService judgeMangePrivilegeService;
  
  @Mock
  private NFileStorageService nFileStorageService;
  
  @InjectMocks
  private ExportUserServiceImpl exportUserService;

  private User testUser;

  @BeforeEach
  void setUp() {
    testUser = User.builder()
        .tenantId("123456")
        .userId("78910")
        .build();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试exportUserByRole方法，有角色码的正常导出场景
   */
  @Test
  @DisplayName("正常场景 - 测试exportUserByRole方法有角色码完整流程")
  void testExportUserByRole_WithRoleCode_FullFlow() throws IOException {
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      // 准备测试数据
      String roleCode = "ROLE_001";
      i18nMock.when(() -> I18N.text(any())).thenReturn("测试文本");
      
      // Mock权限检查
      JudgeManageRangeInfo.Result manageRangeResult = mock(JudgeManageRangeInfo.Result.class);
      when(manageRangeResult.isHasAbility()).thenReturn(true);
      when(judgeMangePrivilegeService.getUserMangeRangeInfo(any(SessionContext.class), anyString())).thenReturn(manageRangeResult);
      
             // Mock角色信息查询
       RoleInfoModel.Result roleInfoResult = mock(RoleInfoModel.Result.class);
       Map<String, List<RoleInfoModel.UserRolePojo>> resultMap = new HashMap<>();
       RoleInfoModel.UserRolePojo rolePojo = new RoleInfoModel.UserRolePojo();
       rolePojo.setRoleName("测试角色");
       resultMap.put("roles", Lists.newArrayList(rolePojo));
       when(roleInfoResult.getResult()).thenReturn(resultMap);
       when(userRoleInfoProxy.roleInfo(any(RoleInfoModel.Arg.class), anyMap())).thenReturn(roleInfoResult);
       
       // Mock用户查询
       GetUsersByRole.Result getUsersResult = mock(GetUsersByRole.Result.class);
       when(getUsersResult.isSuccess()).thenReturn(true);
       GetUsersByRole.RoleUserPageInfo getUsersInfo = mock(GetUsersByRole.RoleUserPageInfo.class);
       when(getUsersInfo.getUsers()).thenReturn(Lists.newArrayList("user1", "user2"));
       when(getUsersResult.getResult()).thenReturn(getUsersInfo);
      when(userRoleInfoProxy.getUsersByRole(any(GetUsersByRole.Arg.class), anyMap())).thenReturn(getUsersResult);
      
      // Mock权限过滤
      JudgeMangePrivilegeResult privilegeResult = mock(JudgeMangePrivilegeResult.class);
      when(privilegeResult.getEmployees()).thenReturn(Lists.newArrayList("user1", "user2"));
      when(judgeMangePrivilegeService.judgeMangePrivilege(any(SessionContext.class))).thenReturn(privilegeResult);
      
      // Mock用户信息查询
      UserInfo userInfo1 = new UserInfo();
      userInfo1.setId("user1");
      userInfo1.setName("张三");
      userInfo1.setPost("工程师");
      UserInfo userInfo2 = new UserInfo();
      userInfo2.setId("user2");
      userInfo2.setName("李四");
      userInfo2.setPost("产品经理");
      when(orgService.getUserNameByIds(anyString(), anyString(), anyList())).thenReturn(Lists.newArrayList(userInfo1, userInfo2));
      
      // Mock部门信息查询
      Map<String, QueryDeptInfoByUserIds.MainDeptInfo> deptInfoMap = new HashMap<>();
      QueryDeptInfoByUserIds.MainDeptInfo deptInfo1 = new QueryDeptInfoByUserIds.MainDeptInfo();
      deptInfo1.setDeptName("技术部");
      deptInfoMap.put("user1", deptInfo1);
      when(orgService.getMainDeptInfo(anyString(), anyString(), anyList())).thenReturn(deptInfoMap);
      
      // Mock文件上传
      when(gdsService.getEAByEI(anyString())).thenReturn("test-ea");
      NTempFileUpload.Result uploadResult = mock(NTempFileUpload.Result.class);
      when(uploadResult.getTempFileName()).thenReturn("test-file-path");
      when(nFileStorageService.nTempFileUpload(any(NTempFileUpload.Arg.class), anyString())).thenReturn(uploadResult);
      
      try (MockedStatic<ListUtils> listUtilsMock = mockStatic(ListUtils.class)) {
        listUtilsMock.when(() -> ListUtils.intersection(anyList(), anyList())).thenReturn(Lists.newArrayList("user1", "user2"));
        
        // 执行被测试方法
        ExportUserServiceImpl.Result result = exportUserService.exportUserByRole(testUser, roleCode);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("xlsx", result.getExt());
        assertEquals("test-file-path", result.getPath());
        
        // 验证方法调用
        verify(judgeMangePrivilegeService).getUserMangeRangeInfo(any(SessionContext.class), anyString());
        verify(userRoleInfoProxy).roleInfo(any(RoleInfoModel.Arg.class), anyMap());
        verify(userRoleInfoProxy).getUsersByRole(any(GetUsersByRole.Arg.class), anyMap());
        verify(orgService).getUserNameByIds(anyString(), anyString(), anyList());
        verify(orgService).getMainDeptInfo(anyString(), anyString(), anyList());
        verify(nFileStorageService).nTempFileUpload(any(NTempFileUpload.Arg.class), anyString());
      }
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试exportUserByRole方法，空角色码导出所有用户完整流程
   */
  @Test
  @DisplayName("正常场景 - 测试exportUserByRole方法空角色码导出所有用户完整流程")
  void testExportUserByRole_EmptyRoleCode_FullFlow() throws IOException {
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      // 准备测试数据
      String roleCode = "";
      i18nMock.when(() -> I18N.text(any())).thenReturn("员工角色分布");
      
      // Mock权限检查
      JudgeManageRangeInfo.Result manageRangeResult = mock(JudgeManageRangeInfo.Result.class);
      when(manageRangeResult.isHasAbility()).thenReturn(true);
      when(judgeMangePrivilegeService.getUserMangeRangeInfo(any(SessionContext.class), anyString())).thenReturn(manageRangeResult);
      
      // Mock查询所有用户
      QueryUsers.Result queryUsersResult = mock(QueryUsers.Result.class);
      when(queryUsersResult.isSuccess()).thenReturn(true);
      Map<String, List<String>> usersResult = new HashMap<>();
      usersResult.put("users", Lists.newArrayList("user1", "user2"));
      when(queryUsersResult.getResult()).thenReturn(usersResult);
      when(userRoleInfoProxy.queryUsers(any(QueryUsers.Arg.class), anyMap())).thenReturn(queryUsersResult);
      
      // Mock用户角色信息查询
      QueryRoleInfoListByUsersModel.Result roleInfoResult = mock(QueryRoleInfoListByUsersModel.Result.class);
      Map<String, Map<String, List<QueryRoleInfoListByUsersModel.UserRolePojo>>> roleResultMap = new HashMap<>();
      Map<String, List<QueryRoleInfoListByUsersModel.UserRolePojo>> userRoles = new HashMap<>();
      
      QueryRoleInfoListByUsersModel.UserRolePojo rolePojo1 = new QueryRoleInfoListByUsersModel.UserRolePojo();
      rolePojo1.setRoleName("管理员");
      rolePojo1.setDefaultRole(true);
      userRoles.put("user1", Lists.newArrayList(rolePojo1));
      
      QueryRoleInfoListByUsersModel.UserRolePojo rolePojo2 = new QueryRoleInfoListByUsersModel.UserRolePojo();
      rolePojo2.setRoleName("普通用户");
      rolePojo2.setDefaultRole(true);
      userRoles.put("user2", Lists.newArrayList(rolePojo2));
      
      roleResultMap.put("userRoles", userRoles);
      when(roleInfoResult.getResult()).thenReturn(roleResultMap);
      when(userRoleInfoProxy.queryRoleInfoListByUsers(any(QueryRoleInfoListByUsersModel.Arg.class), anyMap())).thenReturn(roleInfoResult);
      
      // Mock权限过滤和其他服务
      JudgeMangePrivilegeResult privilegeResult = mock(JudgeMangePrivilegeResult.class);
      when(privilegeResult.getEmployees()).thenReturn(Lists.newArrayList("user1", "user2"));
      when(judgeMangePrivilegeService.judgeMangePrivilege(any(SessionContext.class))).thenReturn(privilegeResult);
      
      // Mock用户信息
      UserInfo userInfo1 = new UserInfo();
      userInfo1.setId("user1");
      userInfo1.setName("张三");
      userInfo1.setPost("管理员");
      UserInfo userInfo2 = new UserInfo();
      userInfo2.setId("user2");
      userInfo2.setName("李四");
      userInfo2.setPost("员工");
      when(orgService.getUserNameByIds(anyString(), anyString(), anyList())).thenReturn(Lists.newArrayList(userInfo1, userInfo2));
      when(orgService.getMainDeptInfo(anyString(), anyString(), anyList())).thenReturn(new HashMap<>());
      
      // Mock文件上传
      when(gdsService.getEAByEI(anyString())).thenReturn("test-ea");
      NTempFileUpload.Result uploadResult = mock(NTempFileUpload.Result.class);
      when(uploadResult.getTempFileName()).thenReturn("test-file-path");
      when(nFileStorageService.nTempFileUpload(any(NTempFileUpload.Arg.class), anyString())).thenReturn(uploadResult);
      
      try (MockedStatic<ListUtils> listUtilsMock = mockStatic(ListUtils.class)) {
        listUtilsMock.when(() -> ListUtils.intersection(anyList(), anyList())).thenReturn(Lists.newArrayList("user1", "user2"));
        
        // 执行被测试方法
        ExportUserServiceImpl.Result result = exportUserService.exportUserByRole(testUser, roleCode);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("xlsx", result.getExt());
        assertEquals("员工角色分布", result.getFile_name());
        assertEquals("test-file-path", result.getPath());
        
        // 验证方法调用
        verify(userRoleInfoProxy).queryUsers(any(QueryUsers.Arg.class), anyMap());
        verify(userRoleInfoProxy).queryRoleInfoListByUsers(any(QueryRoleInfoListByUsersModel.Arg.class), anyMap());
      }
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试exportUserByRole方法，无权限场景
   */
  @Test
  @DisplayName("异常场景 - 测试exportUserByRole方法无权限")
  void testExportUserByRoleThrowsPermissionError_NoPermission() {
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      String roleCode = "ROLE_001";
      i18nMock.when(() -> I18N.text(any())).thenReturn("无导出权限");
      
      JudgeManageRangeInfo.Result manageRangeResult = mock(JudgeManageRangeInfo.Result.class);
      when(manageRangeResult.isHasAbility()).thenReturn(false);
      when(judgeMangePrivilegeService.getUserMangeRangeInfo(any(SessionContext.class), anyString())).thenReturn(manageRangeResult);
      
      // 执行并验证异常
      PermissionError exception = assertThrows(PermissionError.class, () -> {
        exportUserService.exportUserByRole(testUser, roleCode);
      });
      
      assertNotNull(exception);
      verify(judgeMangePrivilegeService).getUserMangeRangeInfo(any(SessionContext.class), anyString());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试exportUserByRole方法，查询用户失败场景（由于catch块会捕获异常，所以返回null）
   */
  @Test
  @DisplayName("异常场景 - 测试exportUserByRole方法查询用户失败")
  void testExportUserByRole_QueryUsersFailed() {
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      String roleCode = "ROLE_001";
      i18nMock.when(() -> I18N.text(any())).thenReturn("测试文本");

      // Mock权限检查通过
      JudgeManageRangeInfo.Result manageRangeResult = mock(JudgeManageRangeInfo.Result.class);
      when(manageRangeResult.isHasAbility()).thenReturn(true);
      when(judgeMangePrivilegeService.getUserMangeRangeInfo(any(SessionContext.class), anyString())).thenReturn(manageRangeResult);

             // Mock角色信息查询
       RoleInfoModel.Result roleInfoResult = mock(RoleInfoModel.Result.class);
       Map<String, List<RoleInfoModel.UserRolePojo>> resultMap = new HashMap<>();
       RoleInfoModel.UserRolePojo rolePojo = new RoleInfoModel.UserRolePojo();
       rolePojo.setRoleName("测试角色");
       resultMap.put("roles", Lists.newArrayList(rolePojo));
       when(roleInfoResult.getResult()).thenReturn(resultMap);
      when(userRoleInfoProxy.roleInfo(any(RoleInfoModel.Arg.class), anyMap())).thenReturn(roleInfoResult);

      // Mock用户查询失败
      GetUsersByRole.Result getUsersResult = mock(GetUsersByRole.Result.class);
      when(getUsersResult.isSuccess()).thenReturn(false);
      when(getUsersResult.getErrMessage()).thenReturn("查询用户失败");
      when(userRoleInfoProxy.getUsersByRole(any(GetUsersByRole.Arg.class), anyMap())).thenReturn(getUsersResult);

      // 执行方法，由于catch块会捕获PermissionError异常，所以返回null
      ExportUserServiceImpl.Result result = exportUserService.exportUserByRole(testUser, roleCode);

      // 验证结果为null（异常被catch块捕获）
      assertNull(result);
      verify(userRoleInfoProxy).getUsersByRole(any(GetUsersByRole.Arg.class), anyMap());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试exportData方法，IOException处理
   */
  @Test
  @DisplayName("异常场景 - 测试exportData方法IOException处理")
  void testExportData_IOException() throws IOException {
    // 准备测试数据
    Workbook workbook = mock(Workbook.class);
    String tenantId = "123456";
    String userId = "78910";
    
    // Mock workbook抛出IOException
    doThrow(new IOException("写入失败")).when(workbook).write(any(ByteArrayOutputStream.class));
    when(gdsService.getEAByEI(tenantId)).thenReturn("test-ea");
    
    // 执行并验证异常
    IOException exception = assertThrows(IOException.class, () -> {
      exportUserService.exportData(workbook, tenantId, userId);
    });
    
    assertEquals("写入失败", exception.getMessage());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getUserIds方法，正常过滤用户ID（使用反射调用私有方法）
   */
  @Test
  @DisplayName("正常场景 - 测试getUserIds方法")
  void testGetUserIds_NormalCase() {
    try (MockedStatic<ListUtils> listUtilsMock = mockStatic(ListUtils.class)) {

      // 准备测试数据
      SessionContext sessionContext = new SessionContext();
      List<String> allUserIds = Lists.newArrayList("user1", "user2", "user3");
      List<String> managedEmployees = Lists.newArrayList("user1", "user2");
      List<String> expectedResult = Lists.newArrayList("user1", "user2");

      JudgeMangePrivilegeResult privilegeResult = mock(JudgeMangePrivilegeResult.class);
      when(privilegeResult.getEmployees()).thenReturn(managedEmployees);

      // 配置Mock行为
      when(judgeMangePrivilegeService.judgeMangePrivilege(sessionContext)).thenReturn(privilegeResult);
      listUtilsMock.when(() -> ListUtils.intersection(allUserIds, managedEmployees)).thenReturn(expectedResult);

      // 使用反射调用私有方法
      try {
        java.lang.reflect.Method method = ExportUserServiceImpl.class.getDeclaredMethod("getUserIds", SessionContext.class, List.class);
        method.setAccessible(true);

        // 执行被测试方法
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) method.invoke(exportUserService, sessionContext, allUserIds);

        // 验证结果
        assertEquals(expectedResult, result);
        verify(judgeMangePrivilegeService).judgeMangePrivilege(sessionContext);
      } catch (Exception e) {
        fail("反射调用失败: " + e.getMessage());
      }
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试exportData方法，正常导出数据
   */
  @Test
  @DisplayName("正常场景 - 测试exportData方法")
  void testExportData_NormalCase() throws IOException {
    // 准备测试数据
    Workbook workbook = new SXSSFWorkbook();
    String tenantId = "123456";
    String userId = "78910";
    String expectedPath = "test-file-path";
    
    // 配置Mock行为
    when(gdsService.getEAByEI(tenantId)).thenReturn("test-ea");
    
    // Mock uploadFile方法
    ExportUserServiceImpl spyService = spy(exportUserService);
    doReturn(expectedPath).when(spyService).uploadFile(eq("test-ea"), eq(userId), any(ByteArrayOutputStream.class));
    
    // 执行被测试方法
    String result = spyService.exportData(workbook, tenantId, userId);
    
    // 验证结果
    assertEquals(expectedPath, result);
    verify(gdsService).getEAByEI(tenantId);
    verify(spyService).uploadFile(eq("test-ea"), eq(userId), any(ByteArrayOutputStream.class));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试uploadFile方法，正常上传文件
   */
  @Test
  @DisplayName("正常场景 - 测试uploadFile方法")
  void testUploadFile_NormalCase() {
    // 准备测试数据
    String ea = "test-ea";
    String user = "78910";
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    outputStream.write("test data".getBytes(), 0, "test data".length());
    
    NTempFileUpload.Result uploadResult = mock(NTempFileUpload.Result.class);
    when(uploadResult.getTempFileName()).thenReturn("temp-file-name");
    
    // 配置Mock行为
    when(nFileStorageService.nTempFileUpload(any(NTempFileUpload.Arg.class), eq(ea))).thenReturn(uploadResult);
    
    // 执行被测试方法
    String result = exportUserService.uploadFile(ea, user, outputStream);
    
    // 验证结果
    assertEquals("temp-file-name", result);
    verify(nFileStorageService).nTempFileUpload(any(NTempFileUpload.Arg.class), eq(ea));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试uploadFile方法，返回结果为null场景
   */
  @Test
  @DisplayName("异常场景 - 测试uploadFile方法返回结果为null")
  void testUploadFile_NullResult() {
    String ea = "test-ea";
    String user = "78910";
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    
    // Mock返回null
    when(nFileStorageService.nTempFileUpload(any(NTempFileUpload.Arg.class), eq(ea))).thenReturn(null);
    
    // 执行并验证异常
    MetaDataException exception = assertThrows(MetaDataException.class, () -> {
      exportUserService.uploadFile(ea, user, outputStream);
    });
    
    assertTrue(exception.getMessage().contains("Can not upload excel file"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试createSheetWithTitle方法，有角色码场景
   */
  @Test
  @DisplayName("正常场景 - 测试createSheetWithTitle方法有角色码")
  void testCreateSheetWithTitle_WithRoleCode() {
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      // 准备测试数据
      Workbook workbook = new SXSSFWorkbook();
      String roleCode = "ROLE_001";
      List<String> labels = Arrays.asList("姓名", "部门", "职位", "主角色", "角色");
      
      // Mock I18N.text方法
      i18nMock.when(() -> I18N.text(any())).thenReturn("员工角色");
      
      // 执行被测试方法
      Sheet result = exportUserService.createSheetWithTitle(workbook, roleCode, labels);
      
      // 验证结果
      assertNotNull(result);
      assertEquals("员工角色", result.getSheetName());
      
      // 验证表头 - 当roleCode不为空时，只创建前3列（labels.size() - 2 = 3）
      Row headerRow = result.getRow(0);
      assertNotNull(headerRow);
      assertEquals(3, headerRow.getPhysicalNumberOfCells());
      assertEquals("姓名", headerRow.getCell(0).getStringCellValue());
      assertEquals("部门", headerRow.getCell(1).getStringCellValue());
      assertEquals("职位", headerRow.getCell(2).getStringCellValue());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试createSheetWithTitle方法，空角色码场景
   */
  @Test
  @DisplayName("正常场景 - 测试createSheetWithTitle方法空角色码")
  void testCreateSheetWithTitle_EmptyRoleCode() {
    try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
      // 准备测试数据
      Workbook workbook = new SXSSFWorkbook();
      String roleCode = "";
      List<String> labels = Arrays.asList("姓名", "部门", "职位", "主角色", "角色");
      
      // Mock I18N.text方法
      i18nMock.when(() -> I18N.text(any())).thenReturn("员工角色");
      
      // 执行被测试方法
      Sheet result = exportUserService.createSheetWithTitle(workbook, roleCode, labels);
      
      // 验证结果
      assertNotNull(result);
      assertEquals("员工角色", result.getSheetName());
      
      // 验证表头 - 当roleCode为空时，创建所有列
      Row headerRow = result.getRow(0);
      assertNotNull(headerRow);
      assertEquals(5, headerRow.getPhysicalNumberOfCells());
      assertEquals("姓名", headerRow.getCell(0).getStringCellValue());
      assertEquals("部门", headerRow.getCell(1).getStringCellValue());
      assertEquals("职位", headerRow.getCell(2).getStringCellValue());
      assertEquals("主角色", headerRow.getCell(3).getStringCellValue());
      assertEquals("角色", headerRow.getCell(4).getStringCellValue());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试generateSheet方法，正常生成数据行
   */
  @Test
  @DisplayName("正常场景 - 测试generateSheet方法")
  void testGenerateSheet_NormalCase() {
    // 准备测试数据
    Workbook workbook = new SXSSFWorkbook();
    Sheet sheet = workbook.createSheet("TestSheet");
    
    List<List<String>> data = Lists.newArrayList();
    data.add(Lists.newArrayList("张三", "技术部", "工程师", "开发角色", "开发角色 测试角色"));
    data.add(Lists.newArrayList("李四", "产品部", "产品经理", "产品角色", "产品角色"));
    
    // 执行被测试方法（使用index=0，与实际调用保持一致）
    exportUserService.generateSheet(sheet, 0, data);
    
    // 验证结果
    assertEquals(2, sheet.getPhysicalNumberOfRows()); // 包含数据行，从第1行开始
    
    // 验证第一行数据（实际在第1行，因为index=0，循环从i=1开始，所以是0+1=1）
    Row firstDataRow = sheet.getRow(1);
    assertNotNull(firstDataRow);
    assertEquals("张三", firstDataRow.getCell(0).getStringCellValue());
    assertEquals("技术部", firstDataRow.getCell(1).getStringCellValue());
    assertEquals("工程师", firstDataRow.getCell(2).getStringCellValue());
    assertEquals("开发角色", firstDataRow.getCell(3).getStringCellValue());
    assertEquals("开发角色 测试角色", firstDataRow.getCell(4).getStringCellValue());
    
    // 验证第二行数据（实际在第2行）
    Row secondDataRow = sheet.getRow(2);
    assertNotNull(secondDataRow);
    assertEquals("李四", secondDataRow.getCell(0).getStringCellValue());
    assertEquals("产品部", secondDataRow.getCell(1).getStringCellValue());
    assertEquals("产品经理", secondDataRow.getCell(2).getStringCellValue());
    assertEquals("产品角色", secondDataRow.getCell(3).getStringCellValue());
    assertEquals("产品角色", secondDataRow.getCell(4).getStringCellValue());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试generateSheet方法，空字符串处理
   */
  @Test
  @DisplayName("边界场景 - 测试generateSheet方法空字符串处理")
  void testGenerateSheet_EmptyStringHandling() {
    try (MockedStatic<Strings> stringsMock = mockStatic(Strings.class)) {
      // 准备测试数据
      Workbook workbook = new SXSSFWorkbook();
      Sheet sheet = workbook.createSheet("TestSheet");
      
      List<List<String>> data = Lists.newArrayList();
      data.add(Lists.newArrayList("张三", "", null, "角色"));
      
      // Mock Strings.isNullOrEmpty
      stringsMock.when(() -> Strings.isNullOrEmpty("")).thenReturn(true);
      stringsMock.when(() -> Strings.isNullOrEmpty(null)).thenReturn(true);
      stringsMock.when(() -> Strings.isNullOrEmpty("张三")).thenReturn(false);
      stringsMock.when(() -> Strings.isNullOrEmpty("角色")).thenReturn(false);
      
      // 执行被测试方法
      exportUserService.generateSheet(sheet, 0, data);
      
      // 验证结果
      Row dataRow = sheet.getRow(1);
      assertNotNull(dataRow);
      assertEquals("张三", dataRow.getCell(0).getStringCellValue());
      assertEquals("", dataRow.getCell(1).getStringCellValue()); // 空字符串
      assertEquals("", dataRow.getCell(2).getStringCellValue()); // null处理为空字符串
      assertEquals("角色", dataRow.getCell(3).getStringCellValue());
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试uploadFile方法，上传失败场景
   */
  @Test
  @DisplayName("异常场景 - 测试uploadFile方法上传失败")
  void testUploadFileThrowsMetaDataException_UploadFailed() {
    try (MockedStatic<Strings> stringsMock = mockStatic(Strings.class)) {
      
      // 准备测试数据
      String ea = "test-ea";
      String user = "78910";
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      
      NTempFileUpload.Result uploadResult = mock(NTempFileUpload.Result.class);
      when(uploadResult.getTempFileName()).thenReturn(null);
      
      // 配置Mock行为
      stringsMock.when(() -> Strings.isNullOrEmpty(null)).thenReturn(true);
      when(nFileStorageService.nTempFileUpload(any(NTempFileUpload.Arg.class), eq(ea))).thenReturn(uploadResult);
      
      // 执行并验证异常
      MetaDataException exception = assertThrows(MetaDataException.class, () -> {
        exportUserService.uploadFile(ea, user, outputStream);
      });
      
      // 验证异常信息
      assertTrue(exception.getMessage().contains("Can not upload excel file"));
      verify(nFileStorageService).nTempFileUpload(any(NTempFileUpload.Arg.class), eq(ea));
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildAuthContext方法（使用反射调用私有方法）
   */
  @Test
  @DisplayName("正常场景 - 测试buildAuthContext方法")
  void testBuildAuthContext() {
    try {
      // 使用反射调用私有方法
      java.lang.reflect.Method method = ExportUserServiceImpl.class.getDeclaredMethod("buildAuthContext", User.class);
      method.setAccessible(true);

      // 执行被测试方法
      AuthContext result = (AuthContext) method.invoke(exportUserService, testUser);

      // 验证结果
      assertNotNull(result);
      assertEquals(testUser.getTenantId(), result.getTenantId());
      assertEquals(testUser.getUserId(), result.getUserId());
    } catch (Exception e) {
      fail("反射调用失败: " + e.getMessage());
    }
  }
}
