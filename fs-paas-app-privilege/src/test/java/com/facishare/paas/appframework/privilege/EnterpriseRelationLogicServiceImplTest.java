package com.facishare.paas.appframework.privilege;

import com.facishare.organization.adapter.api.model.organizationwithouter.OrganizationEmployee;
import com.facishare.paas.appframework.common.service.OuterOrganizationService;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.privilege.dto.SupportEnterpriseRelationResult;
import com.facishare.paas.appframework.privilege.model.EnterpriseRelationTeamMemberStrategy;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.fxiaoke.enterpriserelation2.result.OuterAccountVo;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.fxiaoke.enterpriserelation2.result.data.RelationEmployeeIdInfoData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 测试EnterpriseRelationLogicServiceImpl类的企业关联逻辑功能
 * 覆盖主要业务方法的各种场景，包括正常流程、异常处理和边界条件
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class EnterpriseRelationLogicServiceImplTest {

    @Mock
    private EnterpriseRelationServiceProxy enterpriseRelationService;

    @Mock
    private OuterOrganizationService outerOrganizationService;

    @Mock
    private LicenseService licenseService;

    @Mock
    private OptionalFeaturesService optionalFeaturesService;

    @InjectMocks
    private EnterpriseRelationLogicServiceImpl enterpriseRelationLogicService;

    private User testUser;
    private User guestUser;
    private IObjectDescribe mockDescribe;
    private IObjectData mockObjectData;
    private Collection<IObjectData> mockDataList;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .tenantId("123456")
                .userId("78910")
                .build();
        
        guestUser = User.builder()
                .tenantId("123456")
                .userId("78911")
                .outUserId("**********")
                .outTenantId("**********")
                .build();

        mockDescribe = mock(IObjectDescribe.class);
        mockObjectData = mock(IObjectData.class);
        mockDataList = Lists.newArrayList(mockObjectData);
    }

    // ==================== fillOutOwner方法测试 ====================

    @Test
    @DisplayName("fillOutOwner - 从对象场景，直接返回")
    void testFillOutOwner_SlaveObject() {
        try (MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            describeExtMock.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(true);

            enterpriseRelationLogicService.fillOutOwner(testUser, mockDescribe, mockDataList);

            verify(mockDescribeExt).isSlaveObject();
            verify(mockDescribeExt, never()).getSupportRelationOuterOwnerFields();
        }
    }

    @Test
    @DisplayName("fillOutOwner - 正常场景，填充外部负责人")
    void testFillOutOwner_NormalCase() {
        try (MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class);
             MockedStatic<ObjectDataExt> objectDataExtMock = mockStatic(ObjectDataExt.class)) {
            
            // 准备测试数据
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            IObjectReferenceField mockReferenceField = mock(IObjectReferenceField.class);
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            
            // 配置Mock行为
            describeExtMock.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            objectDataExtMock.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);
            when(mockDescribeExt.getSupportRelationOuterOwnerFields()).thenReturn(Lists.newArrayList(mockReferenceField));
            when(mockReferenceField.getApiName()).thenReturn("customerId");
            when(mockReferenceField.getTargetApiName()).thenReturn("customer");
            when(mockObjectData.getOutOwner()).thenReturn(Lists.newArrayList());
            when(mockObjectData.get("customerId", String.class)).thenReturn("customer001");
            
            collectionUtilsMock.when(() -> CollectionUtils.empty(Lists.newArrayList())).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.empty(Sets.newHashSet("customer001"))).thenReturn(false);
            
            // Mock下游企业查询
            Map<String, RelationDownstreamResult> downstreamMap = Maps.newHashMap();
            RelationDownstreamResult downstreamResult = mock(RelationDownstreamResult.class);
            when(downstreamResult.getDownstreamOuterTenantId()).thenReturn(888);
            downstreamMap.put("customer001", downstreamResult);
            when(enterpriseRelationService.getRelationDownstreamInfo(anyString(), anyString(), anySet())).thenReturn(downstreamMap);
            
            // Mock外部用户查询
            User outUser = User.builder().outTenantId("888").outUserId("999").build();
            when(outerOrganizationService.getOwnerOutUserByOutTenant(testUser, "888")).thenReturn(Optional.of(outUser));

            enterpriseRelationLogicService.fillOutOwner(testUser, mockDescribe, mockDataList);

            verify(mockDescribeExt).isSlaveObject();
            verify(mockDescribeExt).getSupportRelationOuterOwnerFields();
            verify(mockObjectData).setOutOwner(Lists.newArrayList("999"));
            verify(mockObjectData).setOutTenantId("888");
        }
    }

    @Test
    @DisplayName("fillOutOwner - 空引用数据ID场景")
    void testFillOutOwner_EmptyReferenceIds() {
        try (MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            IObjectReferenceField mockReferenceField = mock(IObjectReferenceField.class);
            
            describeExtMock.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);
            when(mockDescribeExt.getSupportRelationOuterOwnerFields()).thenReturn(Lists.newArrayList(mockReferenceField));
            when(mockReferenceField.getApiName()).thenReturn("customerId");
            when(mockObjectData.getOutOwner()).thenReturn(Lists.newArrayList());
            when(mockObjectData.get("customerId", String.class)).thenReturn("");
            
            collectionUtilsMock.when(() -> CollectionUtils.empty(Lists.newArrayList())).thenReturn(true);
            collectionUtilsMock.when(() -> CollectionUtils.empty(Sets.newHashSet())).thenReturn(true);

            enterpriseRelationLogicService.fillOutOwner(testUser, mockDescribe, mockDataList);

            verify(enterpriseRelationService, never()).getRelationDownstreamInfo(anyString(), anyString(), anySet());
        }
    }

    // ==================== fillOutTeamMember方法测试 ====================

    @Test
    @DisplayName("fillOutTeamMember - 灰度开关关闭")
    void testFillOutTeamMember_GrayConfigDisabled() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class)) {
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, testUser.getTenantId())).thenReturn(false);

            enterpriseRelationLogicService.fillOutTeamMember(testUser, mockDescribe, mockDataList);

            grayConfigMock.verify(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, testUser.getTenantId()));
        }
    }

    @Test
    @DisplayName("fillOutTeamMember - 从对象场景")
    void testFillOutTeamMember_SlaveObject() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class);
             MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class)) {
            
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, testUser.getTenantId())).thenReturn(true);
            describeExtMock.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(true);

            enterpriseRelationLogicService.fillOutTeamMember(testUser, mockDescribe, mockDataList);

            verify(mockDescribeExt).isSlaveObject();
        }
    }

    @Test
    @DisplayName("fillOutTeamMember - 外部访客用户")
    void testFillOutTeamMember_OutGuestUser() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class);
             MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class)) {
            
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, guestUser.getTenantId())).thenReturn(true);
            describeExtMock.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);

            enterpriseRelationLogicService.fillOutTeamMember(guestUser, mockDescribe, mockDataList);

            verify(optionalFeaturesService, never()).findOptionalFeaturesSwitch(anyString(), any());
        }
    }

    @Test
    @DisplayName("fillOutTeamMember - 相关团队功能未开启")
    void testFillOutTeamMember_RelatedTeamDisabled() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class);
             MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class)) {
            
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO();
            optionalFeaturesSwitch.setIsRelatedTeamEnabled(false);
            
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, testUser.getTenantId())).thenReturn(true);
            describeExtMock.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);
            when(optionalFeaturesService.findOptionalFeaturesSwitch(testUser.getTenantId(), mockDescribe)).thenReturn(optionalFeaturesSwitch);

            enterpriseRelationLogicService.fillOutTeamMember(testUser, mockDescribe, mockDataList);

            verify(optionalFeaturesService).findOptionalFeaturesSwitch(testUser.getTenantId(), mockDescribe);
        }
    }

    @Test
    @DisplayName("fillOutTeamMember - 无关联字段")
    void testFillOutTeamMember_NoRelationFields() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class);
             MockedStatic<ObjectDescribeExt> describeExtMock = mockStatic(ObjectDescribeExt.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            
            ObjectDescribeExt mockDescribeExt = mock(ObjectDescribeExt.class);
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = new OptionalFeaturesSwitchDTO();
            optionalFeaturesSwitch.setIsRelatedTeamEnabled(true);
            
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, testUser.getTenantId())).thenReturn(true);
            describeExtMock.when(() -> ObjectDescribeExt.of(mockDescribe)).thenReturn(mockDescribeExt);
            when(mockDescribeExt.isSlaveObject()).thenReturn(false);
            when(mockDescribeExt.getSupportRelationFields()).thenReturn(Lists.newArrayList());
            when(optionalFeaturesService.findOptionalFeaturesSwitch(testUser.getTenantId(), mockDescribe)).thenReturn(optionalFeaturesSwitch);
            collectionUtilsMock.when(() -> CollectionUtils.empty(Lists.newArrayList())).thenReturn(true);

            enterpriseRelationLogicService.fillOutTeamMember(testUser, mockDescribe, mockDataList);

            verify(mockDescribeExt).getSupportRelationFields();
        }
    }

    // ==================== syncOutTenantIdFromOutUser方法测试 ====================

    @Test
    @DisplayName("syncOutTenantIdFromOutUser - 正常同步外部租户ID")
    void testSyncOutTenantIdFromOutUser_NormalCase() {
        try (MockedStatic<ObjectDataExt> objectDataExtMock = mockStatic(ObjectDataExt.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            OrganizationEmployee mockEmployee = mock(OrganizationEmployee.class);
            
            objectDataExtMock.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getOutOwnerId()).thenReturn(Optional.of("999"));
            when(mockEmployee.getEmployeeId()).thenReturn("999");
            when(mockEmployee.getMainDepartmentId()).thenReturn("888");
            
            collectionUtilsMock.when(() -> CollectionUtils.empty(Lists.newArrayList("999"))).thenReturn(false);
            when(outerOrganizationService.batchGetEmployee(testUser.getTenantId(), Lists.newArrayList("999")))
                    .thenReturn(Lists.newArrayList(mockEmployee));

            enterpriseRelationLogicService.syncOutTenantIdFromOutUser(testUser, mockDescribe, mockDataList);

            verify(mockObjectDataExt).setOutTenantId("888");
            verify(mockObjectDataExt).synchronizeOutTeamMemberOwner("888", "999");
        }
    }

    @Test
    @DisplayName("syncOutTenantIdFromOutUser - 空外部负责人列表")
    void testSyncOutTenantIdFromOutUser_EmptyOutOwners() {
        try (MockedStatic<ObjectDataExt> objectDataExtMock = mockStatic(ObjectDataExt.class);
             MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            
            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            
            objectDataExtMock.when(() -> ObjectDataExt.of(mockObjectData)).thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getOutOwnerId()).thenReturn(Optional.empty());
            collectionUtilsMock.when(() -> CollectionUtils.empty(Lists.newArrayList())).thenReturn(true);

            enterpriseRelationLogicService.syncOutTenantIdFromOutUser(testUser, mockDescribe, mockDataList);

            verify(outerOrganizationService, never()).batchGetEmployee(anyString(), anyList());
        }
    }

    // ==================== getOwnerOutUserByDataIds方法测试 ====================

    @Test
    @DisplayName("getOwnerOutUserByDataIds - 正常获取外部用户")
    void testGetOwnerOutUserByDataIds_NormalCase() {
        // Mock下游企业查询
        Map<String, RelationDownstreamResult> downstreamMap = Maps.newHashMap();
        RelationDownstreamResult downstreamResult = mock(RelationDownstreamResult.class);
        when(downstreamResult.getDownstreamOuterTenantId()).thenReturn(888);
        downstreamMap.put("customer001", downstreamResult);
        when(enterpriseRelationService.getRelationDownstreamInfo("123456", "customer", Sets.newHashSet("customer001")))
                .thenReturn(downstreamMap);
        
        // Mock外部用户查询
        User outUser = User.builder().outTenantId("888").outUserId("999").build();
        when(outerOrganizationService.getOwnerOutUserByOutTenant(testUser, "888")).thenReturn(Optional.of(outUser));

        Map<String, User> result = enterpriseRelationLogicService.getOwnerOutUserByDataIds(testUser, "customer", Sets.newHashSet("customer001"));

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("customer001"));
        assertEquals("888", result.get("customer001").getOutTenantId());
        assertEquals("999", result.get("customer001").getOutUserId());
    }

    @Test
    @DisplayName("getOwnerOutUserByDataIds - 空结果")
    void testGetOwnerOutUserByDataIds_EmptyResult() {
        when(enterpriseRelationService.getRelationDownstreamInfo("123456", "customer", Sets.newHashSet("customer001")))
                .thenReturn(Maps.newHashMap());

        Map<String, User> result = enterpriseRelationLogicService.getOwnerOutUserByDataIds(testUser, "customer", Sets.newHashSet("customer001"));

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    // ==================== 许可证相关方法测试 ====================

    @ParameterizedTest
    @CsvSource({
            "true, true",
            "false, false"
    })
    @DisplayName("supportInterconnectBaseAppLicense - 参数化测试")
    void testSupportInterconnectBaseAppLicense(boolean licenseResult, boolean expectedResult) {
        String tenantId = "123456";
        Map<String, Boolean> licenseMap = Maps.newHashMap();
        licenseMap.put(ModuleCode.INTERCONNECT_APP_BASIC_APP, licenseResult);

        when(licenseService.existModule(tenantId, Sets.newHashSet(ModuleCode.INTERCONNECT_APP_BASIC_APP))).thenReturn(licenseMap);

        boolean result = enterpriseRelationLogicService.supportInterconnectBaseAppLicense(tenantId);

        assertEquals(expectedResult, result);
        verify(licenseService).existModule(tenantId, Sets.newHashSet(ModuleCode.INTERCONNECT_APP_BASIC_APP));
    }

    @Test
    @DisplayName("supportInterconnectBaseAppLicense - 许可证结果为null")
    void testSupportInterconnectBaseAppLicense_NullLicense() {
        String tenantId = "123456";
        Map<String, Boolean> licenseMap = Maps.newHashMap();
        // 不放入任何值，模拟getOrDefault情况

        when(licenseService.existModule(tenantId, Sets.newHashSet(ModuleCode.INTERCONNECT_APP_BASIC_APP))).thenReturn(licenseMap);

        boolean result = enterpriseRelationLogicService.supportInterconnectBaseAppLicense(tenantId);

        assertFalse(result);
    }

    @Test
    @DisplayName("isSupportEnterpriseRelation - 不支持许可证")
    void testIsSupportEnterpriseRelation_NoLicense() {
        String tenantId = "123456";
        Map<String, Boolean> licenseResult = Maps.newHashMap();
        licenseResult.put(ModuleCode.INTERCONNECT_APP_BASIC_APP, false);

        when(licenseService.existModule(tenantId, Sets.newHashSet(ModuleCode.INTERCONNECT_APP_BASIC_APP))).thenReturn(licenseResult);

        SupportEnterpriseRelationResult result = enterpriseRelationLogicService.isSupportEnterpriseRelation(tenantId);

        assertNotNull(result);
        assertFalse(result.isSupportInterconnectBaseAppLicense());
        assertFalse(result.isSupportTeamMember());
    }

    @Test
    @DisplayName("isSupportEnterpriseRelation - 支持许可证和团队成员")
    void testIsSupportEnterpriseRelation_SupportedWithTeamMember() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class)) {
            String tenantId = "123456";
            Map<String, Boolean> licenseResult = Maps.newHashMap();
            licenseResult.put(ModuleCode.INTERCONNECT_APP_BASIC_APP, true);

            when(licenseService.existModule(tenantId, Sets.newHashSet(ModuleCode.INTERCONNECT_APP_BASIC_APP))).thenReturn(licenseResult);
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, tenantId)).thenReturn(true);

            SupportEnterpriseRelationResult result = enterpriseRelationLogicService.isSupportEnterpriseRelation(tenantId);

            assertNotNull(result);
            assertTrue(result.isSupportInterconnectBaseAppLicense());
            assertTrue(result.isSupportTeamMember());
        }
    }

    @Test
    @DisplayName("isSupportEnterpriseRelation - 支持许可证但不支持团队成员")
    void testIsSupportEnterpriseRelation_SupportedWithoutTeamMember() {
        try (MockedStatic<UdobjGrayConfig> grayConfigMock = mockStatic(UdobjGrayConfig.class)) {
            String tenantId = "123456";
            Map<String, Boolean> licenseResult = Maps.newHashMap();
            licenseResult.put(ModuleCode.INTERCONNECT_APP_BASIC_APP, true);

            when(licenseService.existModule(tenantId, Sets.newHashSet(ModuleCode.INTERCONNECT_APP_BASIC_APP))).thenReturn(licenseResult);
            grayConfigMock.when(() -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENTERPRISE_RELATION_SUPPORT_TEAM_MEMBER, tenantId)).thenReturn(false);

            SupportEnterpriseRelationResult result = enterpriseRelationLogicService.isSupportEnterpriseRelation(tenantId);

            assertNotNull(result);
            assertTrue(result.isSupportInterconnectBaseAppLicense());
            assertFalse(result.isSupportTeamMember());
        }
    }

    // ==================== listOuterInfo方法测试 ====================

    @Test
    @DisplayName("listOuterInfo - 正常获取外部信息")
    void testListOuterInfo_NormalCase() {
        try (MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            Collection<String> names = Lists.newArrayList("name1", "name2");
            Map<String, List<OuterAccountVo>> listMap = Maps.newHashMap();
            OuterAccountVo outerAccount = mock(OuterAccountVo.class);
            when(outerAccount.getOuterUid()).thenReturn(999L);
            when(outerAccount.getOuterTenantId()).thenReturn(888L);
            listMap.put("name1", Lists.newArrayList(outerAccount));

            collectionUtilsMock.when(() -> CollectionUtils.empty(listMap)).thenReturn(false);
            when(outerOrganizationService.listOuterOwnerByNames(testUser, names)).thenReturn(listMap);

            List<UserInfo> result = enterpriseRelationLogicService.listOuterInfo(testUser, names);

            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("999", result.get(0).getId());
            assertEquals("888", result.get(0).getTenantId());
            assertEquals("name1", result.get(0).getName());
        }
    }

    @Test
    @DisplayName("listOuterInfo - 空结果")
    void testListOuterInfo_EmptyResult() {
        try (MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            Collection<String> names = Lists.newArrayList("name1", "name2");

            collectionUtilsMock.when(() -> CollectionUtils.empty(Maps.newHashMap())).thenReturn(true);
            when(outerOrganizationService.listOuterOwnerByNames(testUser, names)).thenReturn(Maps.newHashMap());

            List<UserInfo> result = enterpriseRelationLogicService.listOuterInfo(testUser, names);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    // ==================== getOutUserInfoByEnterpriseAndNames方法测试 ====================

    @Test
    @DisplayName("getOutUserInfoByEnterpriseAndNames - 正常获取外部用户信息")
    void testGetOutUserInfoByEnterpriseAndNames_NormalCase() {
        try (MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            Collection<String> names = Lists.newArrayList("name1", "name2");
            RelationEmployeeIdInfoData employeeInfo = mock(RelationEmployeeIdInfoData.class);
            when(employeeInfo.getOuterUserId()).thenReturn(999L);
            when(employeeInfo.getOuterTenantId()).thenReturn(888L);
            when(employeeInfo.getEnterpriseName()).thenReturn("企业A");
            when(employeeInfo.getEmployeeName()).thenReturn("员工1");

            collectionUtilsMock.when(() -> CollectionUtils.empty(Lists.newArrayList(employeeInfo))).thenReturn(false);
            when(outerOrganizationService.batchGetOutUserInfoByEnterpriseAndNames(testUser, names))
                    .thenReturn(Lists.newArrayList(employeeInfo));

            List<UserInfo> result = enterpriseRelationLogicService.getOutUserInfoByEnterpriseAndNames(testUser, names);

            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("999", result.get(0).getId());
            assertEquals("888", result.get(0).getTenantId());
            assertEquals("企业A.员工1", result.get(0).getName());
            assertEquals("企业A.员工1", result.get(0).getNickname());
        }
    }

    @Test
    @DisplayName("getOutUserInfoByEnterpriseAndNames - 空结果")
    void testGetOutUserInfoByEnterpriseAndNames_EmptyResult() {
        try (MockedStatic<CollectionUtils> collectionUtilsMock = mockStatic(CollectionUtils.class)) {
            Collection<String> names = Lists.newArrayList("name1", "name2");

            collectionUtilsMock.when(() -> CollectionUtils.empty(Lists.newArrayList())).thenReturn(true);
            when(outerOrganizationService.batchGetOutUserInfoByEnterpriseAndNames(testUser, names))
                    .thenReturn(Lists.newArrayList());

            List<UserInfo> result = enterpriseRelationLogicService.getOutUserInfoByEnterpriseAndNames(testUser, names);

            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }
} 