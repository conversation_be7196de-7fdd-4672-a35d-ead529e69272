package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.privilege.dto.DataPrivilegeCalcProgress;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试DataAuthServiceImpl类的方法
 */
@ExtendWith(MockitoExtension.class)
class DataAuthServiceImplTest {

    @Mock
    private DataAuthProxy dataAuthProxy;

    @InjectMocks
    private DataAuthServiceImpl dataAuthService;

    private String testTenantId;

    @BeforeEach
    void setUp() {
        testTenantId = "123456";
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试calcProgress方法正常场景，成功获取进度信息
     */
    @Test
    @DisplayName("正常场景 - calcProgress方法成功获取进度信息")
    void testCalcProgress_Success() {
        // 准备测试数据
        DataPrivilegeCalcProgress.Body expectedBody = new DataPrivilegeCalcProgress.Body();
        expectedBody.setTenantId(testTenantId);
        expectedBody.setTotal(100);
        expectedBody.setCurrent(50);
        expectedBody.setPercent(50);
        expectedBody.setDone(false);
        
        DataPrivilegeCalcProgress.Result mockResult = DataPrivilegeCalcProgress.Result.builder()
                .code(0)
                .message("成功")
                .body(expectedBody)
                .build();

        // 配置Mock行为
        when(dataAuthProxy.calcProgress(any(DataPrivilegeCalcProgress.Arg.class))).thenReturn(mockResult);

        // 执行被测试方法
        DataPrivilegeCalcProgress.Body result = dataAuthService.calcProgress(testTenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedBody.getTenantId(), result.getTenantId());
        assertEquals(expectedBody.getTotal(), result.getTotal());
        assertEquals(expectedBody.getCurrent(), result.getCurrent());
        assertEquals(expectedBody.getPercent(), result.getPercent());
        assertEquals(expectedBody.getDone(), result.getDone());

        // 验证Mock交互
        verify(dataAuthProxy, times(1)).calcProgress(argThat(arg -> 
            arg.getTenantId().equals(testTenantId)
        ));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试calcProgress方法异常场景，接口返回错误的参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideErrorTestData")
    @DisplayName("异常场景 - calcProgress方法接口返回错误")
    void testCalcProgressThrowsValidateException_ParameterizedTest(int errorCode, String errorMessage) {
        // 准备测试数据
        DataPrivilegeCalcProgress.Result mockResult = DataPrivilegeCalcProgress.Result.builder()
                .code(errorCode)
                .message(errorMessage)
                .build();

        // 配置Mock行为
        when(dataAuthProxy.calcProgress(any(DataPrivilegeCalcProgress.Arg.class))).thenReturn(mockResult);

        // 执行并验证异常
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            dataAuthService.calcProgress(testTenantId);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证Mock交互
        verify(dataAuthProxy, times(1)).calcProgress(any(DataPrivilegeCalcProgress.Arg.class));
    }

    /**
     * 提供参数化测试的错误测试数据
     */
    private static Stream<Arguments> provideErrorTestData() {
        return Stream.of(
            Arguments.of(1, "参数错误"),
            Arguments.of(100, "权限不足"),
            Arguments.of(500, "系统内部错误")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试calcProgress方法边界场景，传入null tenantId
     */
    @Test
    @DisplayName("边界场景 - calcProgress方法传入null tenantId")
    void testCalcProgress_WithNullTenantId() {
        // 准备测试数据
        DataPrivilegeCalcProgress.Body expectedBody = new DataPrivilegeCalcProgress.Body();
        expectedBody.setTenantId(null);
        expectedBody.setTotal(0);
        expectedBody.setCurrent(0);
        expectedBody.setPercent(0);
        expectedBody.setDone(true);
        
        DataPrivilegeCalcProgress.Result mockResult = DataPrivilegeCalcProgress.Result.builder()
                .code(0)
                .message("成功")
                .body(expectedBody)
                .build();

        // 配置Mock行为
        when(dataAuthProxy.calcProgress(any(DataPrivilegeCalcProgress.Arg.class))).thenReturn(mockResult);

        // 执行被测试方法
        DataPrivilegeCalcProgress.Body result = dataAuthService.calcProgress(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedBody.getTenantId(), result.getTenantId());
        assertEquals(expectedBody.getTotal(), result.getTotal());
        assertEquals(expectedBody.getCurrent(), result.getCurrent());
        assertEquals(expectedBody.getPercent(), result.getPercent());
        assertEquals(expectedBody.getDone(), result.getDone());

        // 验证Mock交互
        verify(dataAuthProxy, times(1)).calcProgress(any(DataPrivilegeCalcProgress.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试calcProgress方法正常场景，返回已完成状态
     */
    @Test
    @DisplayName("正常场景 - calcProgress方法返回已完成状态")
    void testCalcProgress_CompletedStatus() {
        // 准备测试数据
        DataPrivilegeCalcProgress.Body expectedBody = new DataPrivilegeCalcProgress.Body();
        expectedBody.setTenantId(testTenantId);
        expectedBody.setTotal(100);
        expectedBody.setCurrent(100);
        expectedBody.setPercent(100);
        expectedBody.setDone(true);
        
        DataPrivilegeCalcProgress.Result mockResult = DataPrivilegeCalcProgress.Result.builder()
                .code(0)
                .message("成功")
                .body(expectedBody)
                .build();

        // 配置Mock行为
        when(dataAuthProxy.calcProgress(any(DataPrivilegeCalcProgress.Arg.class))).thenReturn(mockResult);

        // 执行被测试方法
        DataPrivilegeCalcProgress.Body result = dataAuthService.calcProgress(testTenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedBody.getTenantId(), result.getTenantId());
        assertEquals(expectedBody.getTotal(), result.getTotal());
        assertEquals(expectedBody.getCurrent(), result.getCurrent());
        assertEquals(expectedBody.getPercent(), result.getPercent());
        assertTrue(result.getDone());

        // 验证Mock交互
        verify(dataAuthProxy, times(1)).calcProgress(any(DataPrivilegeCalcProgress.Arg.class));
    }
} 